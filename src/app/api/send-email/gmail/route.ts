import { getUserByEmail } from '@/app/service/user';
import { createSupabaseServer } from '@/lib/supabase/server';
import { OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import { NextRequest, NextResponse } from 'next/server';
import {
  fetchAndInsertEmails,
  fetchEmails,
  fetchMessagesByLabel,
  processEmails,
} from './utils';

const clientId = process.env.GMAIL_CLIENT_ID as string;
const clientSecret = process.env.GMAIL_SECRET_KEY as string;
const redirectUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`;

const oauth2Client = new OAuth2Client(clientId, clientSecret, redirectUrl);

async function createGmailMessage(emailData: any, originalMessageId?: string) {
  let message = `From: ${emailData.from}\r\n`;
  message += `To: ${emailData.to}\r\n`;

  if (emailData.cc) {
    // Add CC if needed
    message += `Cc: ${emailData.cc}\r\n`;
  }

  if (emailData.bcc) {
    // Add BCC if needed
    message += `Bcc: ${emailData.bcc}\r\n`;
  }
  message += `Subject: ${emailData.subject}\r\n`;
  message += 'MIME-Version: 1.0\r\n';

  // Add threading headers (if originalMessageId is available)
  if (originalMessageId) {
    message += `In-Reply-To: ${originalMessageId}\r\n`;
    message += `References: ${originalMessageId}\r\n`;
  }

  if (emailData.attachments && emailData.attachments.length > 0) {
    message += 'Content-Type: multipart/mixed; boundary="boundary_000"\r\n\r\n';
    message += '--boundary_000\r\n';
  }

  message += 'Content-Type: text/html; charset="UTF-8"\r\n\r\n';
  message += `${emailData.html}\r\n`;

  if (emailData.attachments && emailData.attachments.length > 0) {
    for (const attachment of emailData.attachments) {
      message += '--boundary_000\r\n';
      message += `Content-Type: ${attachment.mimeType}; name="${attachment.filename}"\r\n`;
      message += 'Content-Transfer-Encoding: base64\r\n';
      message += `Content-Disposition: attachment; filename="${attachment.filename}"\r\n\r\n`;
      message += `${attachment.content}\r\n`;
    }
    message += '--boundary_000--\r\n';
  }

  return Buffer.from(message)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
}

export async function GET(req: NextRequest) {
  const { searchParams } = req.nextUrl;
  const maxResults = Number(searchParams.get('maxResults') || 10);
  const query = searchParams.get('query') || '';

  try {
    if (query.trim() === '') {
      return NextResponse.json(
        { message: 'No user email query provided' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseServer();
    const { data: userRes } = await supabase.auth.getUser();
    const userEmail = userRes?.user?.email;

    if (!userEmail) {
      return NextResponse.json(
        { message: 'User not authenticated' },
        { status: 401 }
      );
    }

    const tokens = await getUserByEmail(userEmail);

    if (!tokens?.google_refresh_token) {
      return NextResponse.json(
        {
          message:
            'No tokens found, please go to the integration page and grant access',
        },
        { status: 401 }
      );
    }

    // Validate token
    const isAccessTokenValid = await fetch(
      'https://www.googleapis.com/oauth2/v1/tokeninfo',
      {
        method: 'GET',
        headers: { Authorization: `Bearer ${tokens.google_access_token}` },
      }
    );

    if (!isAccessTokenValid.ok) {
      oauth2Client.setCredentials({
        refresh_token: tokens.google_refresh_token,
      });
      const { credentials } = await oauth2Client.refreshAccessToken();
      tokens.google_access_token = credentials.access_token;
    }

    oauth2Client.setCredentials({
      access_token: tokens.google_access_token,
      refresh_token: tokens.google_refresh_token,
    });

    const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

    // If no query, fetch recent emails only
    if (!query) {
      const emails = await fetchEmails(gmail, maxResults);
      return NextResponse.json({ messages: emails });
    }

    // Fetch messages in parallel for INBOX, SENT, and DRAFT
    const [inboxMessages, sentMessages, draftMessages] = await Promise.all([
      fetchMessagesByLabel(gmail, maxResults, query, 'INBOX'),
      fetchMessagesByLabel(gmail, maxResults, query, 'SENT'),
      fetchMessagesByLabel(gmail, maxResults, query, 'DRAFT'),
    ]);

    // Flatten and deduplicate message IDs
    const uniqueMessageIds = [
      ...new Set(
        [...inboxMessages, ...sentMessages, ...draftMessages]
          .map((msg) => msg.id)
          .filter(Boolean)
      ),
    ];

    // Process threads and get metadata
    const { combinedEmails, clientDetails } = await processEmails(
      gmail,
      uniqueMessageIds,
      query,
      supabase
    );

    // Insert new emails & fetch paginated data
    await fetchAndInsertEmails(supabase, clientDetails, tokens, combinedEmails);

    return NextResponse.json({
      messages: combinedEmails
        .slice()
        .sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
        ),
    });
  } catch (error: any) {
    console.error('Error reading emails:', error);
    return NextResponse.json(
      { message: 'Failed to read emails', error: error?.message || error },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const formData = await req.formData();
  const supabase = createSupabaseServer();
  const user = await supabase.auth.getUser();
  const tokens = await getUserByEmail(user?.data?.user?.email as string);
  const isDraft = formData.get('isDraft') === 'true';
  const sendDraft = formData.get('sendDraft') === 'true';
  const draftId = formData.get('draftId');
  const emailData: any = {
    to: formData.get('to'),
    from: formData.get('from'),
    subject: formData.get('subject'),
    html: formData.get('htmlBody'),
    scheduleAt: formData.get('scheduleAt'),
    threadId: formData.get('threadId'),
    attachments: [],
  };

  const formDataEntries = Array.from(formData.entries());

  for (const [key, value] of formDataEntries) {
    if (
      key.startsWith('attachment-') &&
      typeof value === 'object' &&
      'arrayBuffer' in value
    ) {
      try {
        const file = value as File;
        const arrayBuffer = await file.arrayBuffer();
        emailData.attachments.push({
          filename: file.name,
          content: Buffer.from(arrayBuffer).toString('base64'),
          mimeType: file.type,
        });
      } catch (error) {
        console.error('Error processing attachment:', error);
      }
    }
  }
  if (!tokens?.google_refresh_token) {
    return NextResponse.json(
      {
        message: 'Please connect your Gmail account to send emails',
        redirect: '/profile?tab=account-settings',
      },
      { status: 401 }
    );
  }

  try {
    const response = await fetch(
      'https://www.googleapis.com/oauth2/v1/tokeninfo',
      {
        method: 'GET',
        headers: { Authorization: `Bearer ${tokens.google_access_token}` },
      }
    );

    if (!response.ok) {
      oauth2Client.setCredentials({
        refresh_token: tokens.google_refresh_token,
      });
      const { credentials } = await oauth2Client.refreshAccessToken();

      tokens.google_access_token = credentials.access_token;
    }

    oauth2Client.setCredentials({
      access_token: tokens.google_access_token,
      refresh_token: tokens.google_refresh_token,
    });

    const gmail: any = google.gmail({ version: 'v1', auth: oauth2Client });
    const message = await createGmailMessage(emailData, emailData.threadId);
    if (sendDraft) {
      const { data: draftList } = await gmail.users.drafts.list({
        userId: 'me',
      });

      const draft = draftList.drafts?.find(
        (d: any) => d.message?.id === draftId || d.id === draftId
      );

      const { data: draftSent, error } = await gmail.users.drafts.send({
        userId: 'me',
        requestBody: {
          id: draft.id,
        },
      });
      if (error) {
        return NextResponse.json(
          { message: 'Failed to send draft' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        message: 'Draft Sent successfully',
        response: draftSent,
      });
    }
    if (isDraft) {
      const { data: draftResponse, error } = await gmail.users.drafts.create({
        userId: 'me',
        requestBody: {
          message: {
            raw: message,
            threadId:
              emailData.threadId !== 'undefined'
                ? emailData.threadId
                : undefined,
          },
        },
      });

      if (error) {
        return NextResponse.json(
          { message: 'Failed to save draft' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        message: 'Draft saved successfully',
        response: draftResponse,
      });
    }
    const { data: gmailResponse, error } = await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: message,
        labelIds: ['UNREAD', 'INBOX'],
        threadId:
          emailData.threadId !== 'undefined' ? emailData.threadId : undefined,
      },
      sendAt: Math.floor(new Date(emailData.scheduleAt).getTime() / 1000),
      enableRescheduler: true,
    });

    if (error) {
      return NextResponse.json(
        { message: 'Failed to send email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Email sent successfully',
      response: gmailResponse.data,
    });
  } catch (error: any) {
    console.error('Error deleting draft:', error);
    return NextResponse.json(
      { message: 'Failed to send email', error: error.message },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  const body = await req.json();
  const { auth } = createSupabaseServer();
  const user = await auth.getUser();
  const tokens = await getUserByEmail(user?.data?.user?.email as string);

  if (!tokens?.google_refresh_token) {
    return NextResponse.json(
      {
        message:
          'No tokens found, Please go to the integration page and grant access',
      },
      { status: 401 }
    );
  }

  try {
    const response = await fetch(
      'https://www.googleapis.com/oauth2/v1/tokeninfo',
      {
        method: 'GET',
        headers: { Authorization: `Bearer ${tokens.google_access_token}` },
      }
    );

    if (!response.ok) {
      oauth2Client.setCredentials({
        refresh_token: tokens.google_refresh_token,
      });
      const { credentials } = await oauth2Client.refreshAccessToken();

      tokens.google_access_token = credentials.access_token;
    }

    oauth2Client.setCredentials({
      access_token: tokens.google_access_token,
      refresh_token: tokens.google_refresh_token,
    });

    const gmail: any = google.gmail({ version: 'v1', auth: oauth2Client });

    const { data: draftList } = await gmail.users.drafts.list({
      userId: 'me',
    });

    const draft = draftList.drafts?.find(
      (d: any) => d.message?.id === body || d.id === body
    );

    const { data: gmailResponse, error } = await gmail.users.drafts.delete({
      userId: 'me',
      id: draft.id,
    });

    if (error) {
      console.log('ln 494 is error', error);
      return NextResponse.json(
        { message: 'Failed to delete draft' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Draft deleted successfully',
      response: gmailResponse.data,
    });
  } catch (error: any) {
    console.error('Error deleting draft:', error);
    return NextResponse.json(
      { message: 'Error deleting draft:', error: error.message },
      { status: 500 }
    );
  }
}
