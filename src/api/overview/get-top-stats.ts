//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getTopStats(data: any) {
  const response = await fetch(`/api/overview/top`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
  const json = await response.json();
  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch stats');
  }
  return json;
}

type QueryFnType = typeof getTopStats;

export const useGetTopStatsQuery = (
  data: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.overview.topStats, data],
    queryFn: () => getTopStats(data),
    ...config,
  });
};
