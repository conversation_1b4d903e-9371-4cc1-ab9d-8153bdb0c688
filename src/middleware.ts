import { createClient } from '@supabase/supabase-js';
import { NextResponse, type NextRequest } from 'next/server';
import { env } from './constants/env';
import { updateSession } from './lib/supabase/session';
// import { permissions } from './constants/permissions';

// const restrictedRoutes: Record<string, string[]> = {
//   '/admin': [permissions.canViewAdmin],
//   '/overview': [permissions.canViewOverview],
//   '/follow-up': [permissions.canViewFollowUp],
//   '/consultations': [permissions.canViewConsultations],
//   '/invoice': [permissions.canViewReceptionistInvoice],
//   '/wait-list': [permissions.canViewReceptionistWaitList],
//   '/admin/slps': [permissions.canViewAdminSlp],
//   '/admin/users': [permissions.canViewAdminUsers],
// };
const ROLE_REDIRECTS: any = {
  admin: '/overview',
  therapist: (userId: string) => `/slp/${userId}`,
  receptionist: '/consultations',
};

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
export async function middleware(request: NextRequest) {
  const { response, supabase } = await updateSession(request);
  const { data } = await supabase.auth.getUser();
  const { data: userData } = await supabaseAdmin.rpc('get_user_by_email', {
    user_email: data?.user?.email,
  });

  // console.log('session   from middleware is ', data);
  // console.log('error from middleware is ', error);
  // const { data: userData } = await supabase.rpc('get_user_by_email', {
  //   user_email: data?.session?.user?.email,
  // });

  const url = new URL(request.url);
  // const pathname = url.pathname;

  const path = url.pathname;
  const searchParams = url.searchParams.toString();
  const next = searchParams
    ? btoa(`${path}?${searchParams}`)
    : path
      ? btoa(path)
      : btoa('/');
  // Define paths that bypass organization_id checks
  const exemptPaths = ['/sign-up', '/login', '/auth', '/form'];

  // Allow all routes under /form without authentication
  if (url.pathname.startsWith('/form') || url.pathname.startsWith('/client')) {
    return response;
  }
  // console.log('userData in middleware is ', userData);

  // Handle signed-in users
  if (data.user?.email) {
    // // Redirect to /sign-up if no organization_id and current path is not exempt
    if (
      !userData?.organization?.id &&
      !exemptPaths.some((path) => url.pathname.startsWith(path))
    ) {
      return NextResponse.redirect(new URL('/sign-up', request.url), {
        status: 302,
      });
    }

    // for (const [route, requiredPermissions] of Object.entries(
    //   restrictedRoutes
    // )) {
    //   if (pathname.startsWith(route)) {
    //     const hasPermission = requiredPermissions.some((perm) =>
    //       userData?.permissions?.includes(perm)
    //     );

    //     if (!hasPermission) {
    //       return NextResponse.redirect(new URL('/forbidden', request.url), {
    //         status: 302,
    //       });
    //     }
    //   }
    // }

    if (path === '/' && userData?.role) {
      const redirectPath =
        ROLE_REDIRECTS[userData.role as keyof typeof ROLE_REDIRECTS];
      const redirectTo =
        typeof redirectPath === 'function'
          ? redirectPath(userData.id)
          : redirectPath;

      if (redirectTo && redirectTo !== path) {
        return NextResponse.redirect(new URL(redirectTo, request.url), {
          status: 302,
        });
      }
    }

    const headers = new Headers(request.headers);
    headers.set('x-current-path', request.nextUrl.pathname);
    return NextResponse.next({ headers });
    // return response;
  } else {
    // Handle unauthenticated users
    if (
      url.pathname.startsWith('/login') ||
      url.pathname.startsWith('/auth') ||
      url.pathname.startsWith('/book')
    ) {
      return response;
    }
    return NextResponse.redirect(
      new URL(`/login${next ? `?next=${next}` : ''}`, request.url),
      {
        status: 302,
      }
    );
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|api).*)', // Exclude everything under /api
    // '/((?!_next/static|_next/image|favicon.ico|api/quickbooks|api/mail-webhook|api/calendly|api/stripe).*)',
  ],
};
