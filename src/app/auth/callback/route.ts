/* eslint-disable no-constant-condition */
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { type CookieOptions, createServerClient } from '@supabase/ssr';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';

const adminSupabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const nextParam = searchParams.get('next');

  console.log('ln 18', code);

  // if "next" is in param, use it as the redirect URL

  // const next = nextParam && nextParam !== 'null' ? atob(nextParam) : '/';
  let next = '/';
  if (nextParam && nextParam !== 'null') {
    try {
      next = atob(nextParam);
    } catch (error) {
      next = '/';
    }
  }
  console.log('next is ', next);

  if (code) {
    const cookieStore = cookies();
    const supabase = createServerClient(
      env.SUPABASE_URL!,
      env.SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            cookieStore.set({ name, value, ...options });
          },
          remove(name: string, options: CookieOptions) {
            cookieStore.delete({ name, ...options });
          },
        },
      }
    );

    const { error, data } = await supabase.auth.exchangeCodeForSession(code);

    if (error) {
      console.log('ln 47 is an error for exchangeCodeForSession', error);
      return NextResponse.redirect(`${origin}/login?error=true`);
    }

    await supabase.auth.getUser();
    // const { data: authData } = await supabase.auth.getUser();
    // console.log('ln 57 jwt data', authData);

    let { data: fullUser } = await adminSupabase.rpc('get_user_by_email', {
      user_email: data.user?.email,
    });

    if (!fullUser) {
      const fullName = data?.user?.user_metadata?.full_name ?? '';
      const [first_name = '', last_name = ''] = fullName.split(' ');
      const baseSlug = `${first_name.trim().toLowerCase()}${last_name.trim().toLowerCase()}`;
      let event_slug = baseSlug;
      let counter = 1;

      while (true) {
        const { data: slugExist } = await adminSupabase
          .from(tableNames.users)
          .select('id')
          .eq('event_slug', event_slug)
          .single();

        if (!slugExist) break;
        event_slug = `${baseSlug}${counter}`;
        counter++;
      }

      const { data: slugExist } = await adminSupabase
        .from(tableNames.users)
        .select('id')
        .eq('event_slug', event_slug)
        .single();

      if (slugExist) {
        const randomNum = Math.floor(Math.random() * 20) + 1;
        event_slug = `${event_slug}${randomNum}`;
      }
      const res = await adminSupabase
        .from(tableNames.users)
        .insert({
          role: 'therapist',
          office_title: 'therapist',
          email: data?.user?.email,
          first_name,
          last_name,
          status: 'Active',
          permissions_id: ['18', '19'],
          event_slug,
        })
        .select('*')
        .single();
      fullUser = res.data as any;
    }

    const expirationDate = new Date(
      (data.session?.expires_at as number) * 1000
    );
    expirationDate.setFullYear(expirationDate.getFullYear() + 1);

    const cookieData = {
      id: fullUser?.id,
      email: fullUser?.email,
      // expires: expirationDate.toISOString(),
      role: fullUser?.role,
      permissions: fullUser?.permissions,
      organization_id: fullUser?.organization_id,
      organization: {
        id: fullUser?.organization?.id,
        name: fullUser?.organization?.name,
        slug: fullUser?.organization?.slug,
        owner: fullUser?.organization?.owner,
        logo_url: fullUser?.organization?.logo_url,
      },
    };

    cookieStore.set({
      name: 'user_data',
      value: JSON.stringify(cookieData),
      expires: expirationDate,
    });

    if (!error) {
      // if (!fullUser?.onboarding_id) {
      //   return NextResponse.redirect(`${origin}/onboarding`);
      // } else
      if (fullUser.role === 'admin') {
        return NextResponse.redirect(`${origin}/${next}`);
      } else if (fullUser.role === 'therapist') {
        return NextResponse.redirect(`${origin}/slp/${fullUser.id}`);
      } else if (fullUser.role === 'receptionist') {
        return NextResponse.redirect(`${origin}/consultations`);
      }
    }
  }
  return NextResponse.redirect(`${origin}/login?error=true`);
}
