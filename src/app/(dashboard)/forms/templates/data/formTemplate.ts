import { generateUniqueId } from '@/hooks/forms/util';

export interface Question {
  id: any;
  qt: string;
  icon: string;
  type: string;
  default: string;
  page?: number;
  required: boolean;
  title?: string;
  typeTwo?: string;
  options?: string[];
  heading?: string;
  description?: string;
  placeholder?: string;
}

export interface FormTemplate {
  description: string;
  organization_id?: string;
  organization_name?: string;

  title: string;
  slug: string;
  questions: Question[];
  styles?: {
    backgroundColor?: string;
    textColor?: string;
    fontWeight?: string;
    formColor?: string;
    buttonAlignment?: string;
    pageColor?: string;
    fontColor?: string;
    inputBackground?: string;
    fieldSpacing?: string;
    fontSize?: string;
    borderRadius?: string;
    borderColor?: string;
    // New styles
    buttonText?: string;
    buttonColor?: string;
    buttonTextColor?: string;
    buttonSize?: string;
    titleAlignment?: 'left' | 'center' | 'right';
    titleSize?: string;
  };
  image?: string;
}

export const formTemplates: FormTemplate[] = [
  {
    description: 'Basic contact information form',
    title: 'Contact Form',
    slug: 'contact-form',
    styles: {
      backgroundColor: '#fffff',
      textColor: '#212529',
      fontWeight: '400',
      formColor: '#FCF6E9',
      buttonAlignment: 'left',
      pageColor: '#ffdb58',
      fontColor: '#723ffa',
      inputBackground: '#ffffff',
      fieldSpacing: '24px',
      fontSize: '16px',
      borderRadius: '8px',
      borderColor: '#dddddd',
      // New defaults
      buttonText: 'Submit Form',
      buttonColor: '#723ffa',
      buttonTextColor: '#ffffff',
      buttonSize: 'md',
      titleAlignment: 'left',
      titleSize: '24px',
    },
    image: '',
    questions: [
      {
        id: generateUniqueId(),
        qt: 'Email Address',
        icon: 'TfiText',
        type: 'Textbox',
        default: 'true',
        title: 'Short answer',
        required: true,
        placeholder: 'Enter your email',
        page: 1,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Full Name',
        icon: 'TfiText',
        title: 'Short answer',
        type: 'Textbox',
        default: 'false',
        required: true,
        placeholder: 'Enter your full name',
        page: 1,
        heading: 'Short answer',
      },

      {
        id: generateUniqueId(),
        qt: 'Phone Number',
        icon: 'GoHash',
        type: 'Number',
        default: 'false',
        title: 'Number',
        required: false,
        placeholder: 'Enter your phone number',
        page: 1,
        heading: 'Number',
      },
      {
        id: generateUniqueId(),
        qt: 'Message',
        icon: 'TfiText',
        type: 'Textbox',
        default: 'false',
        title: 'Short answer',
        required: false,
        placeholder: 'Enter your message',
        page: 1,
        heading: 'Short answer',
      },
    ],
  },

  {
    description:
      'Please take a moment to fill out our online intake form before your visit. All information is kept completely confidential.',
    title: 'Pediatrics Intake Form',
    slug: 'pediatrics-travel-intake',
    styles: {
      backgroundColor: '#ffffff',
      textColor: '#212529',
      fontWeight: '400',
      formColor: '#FCF6E9',
      buttonAlignment: 'left',
      pageColor: '#ffce58',
      fontColor: '#723ffa',
      inputBackground: '#ffffff',
      fieldSpacing: '24px',
      fontSize: '16px',
      borderRadius: '8px',
      borderColor: '#dddddd',
      buttonText: 'Submit  Form',
      buttonColor: '#723ffa',
      buttonTextColor: '#ffffff',
      buttonSize: 'md',
      titleAlignment: 'left',
      titleSize: '24px',
    },
    image: '',
    questions: [
      {
        id: generateUniqueId(),
        qt: 'First Name',
        icon: 'TfiText',
        type: 'Textbox',

        default: 'false',
        title: 'Short answer',
        required: true,
        placeholder: 'Enter first name',
        page: 1,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Last Name',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'false',
        required: true,
        placeholder: 'Enter last name',
        page: 1,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Email',
        icon: 'TfiText',
        type: 'Textbox',
        default: 'true',
        title: 'Short answer',
        required: true,
        placeholder: 'Enter email',
        page: 1,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Preferred Name',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'false',
        required: false,
        placeholder: 'Enter preferred name',
        page: 1,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Pronouns',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        default: 'false',
        required: false,
        options: ['He/Him', 'She/Her', 'They/Them', 'Other'],
        placeholder: 'Select pronouns',
        page: 1,
        heading: 'Multiple choice',
      },
      {
        id: generateUniqueId(),
        qt: 'Mobile Phone',
        icon: 'GoHash',
        type: 'Number',
        default: 'false',
        title: 'Number',
        required: false,
        placeholder: 'Enter phone',
        page: 1,
        heading: 'Number',
      },
      {
        id: generateUniqueId(),
        qt: 'Date of Birth',
        icon: 'MdDateRange',
        type: 'Date',
        title: 'Date',
        default: 'false',
        required: true,
        placeholder: 'Select date',
        page: 1,
        heading: 'Date',
      },
      {
        id: generateUniqueId(),
        qt: 'Gender',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        default: 'false',
        required: false,
        placeholder: 'Select gender',
        options: ['Male', 'Female', 'Others', 'Prefer not to say'],
        page: 1,
        heading: 'Multiple choice',
        description:
          'Refers to current gender which may be different than insurance records',
      },
      {
        id: generateUniqueId(),
        qt: 'Sex',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        default: 'false',
        required: false,
        page: 1,
        heading: 'Multiple choice',

        options: ['Male', 'Female', 'Others', 'Prefer not to say'],
        placeholder: 'Select sex',
        description: 'Must match insurance records for claims',
      },
      {
        id: generateUniqueId(),
        qt: 'Emergency Contact',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'false',
        page: 1,
        heading: 'Short answer',
        required: false,
        placeholder: 'Enter name',
      },
      {
        id: generateUniqueId(),
        qt: 'Emergency Phone',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        default: 'false',
        required: false,
        placeholder: 'Enter phone',
        page: 2,
        heading: 'Number ',
      },
      {
        id: generateUniqueId(),
        qt: 'Emergency Contact Relationship',
        icon: 'TfiText',
        type: 'Textbox',
        default: 'false',
        title: 'Short answer',
        required: false,
        placeholder: 'Relationship to patient',
        page: 2,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Family Doctor',
        icon: 'TfiText',
        type: 'Textbox',
        default: 'false',
        required: false,
        placeholder: 'Enter family doctor name',
        page: 2,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Family Doctor Phone',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        default: 'false',
        required: false,
        placeholder: 'Enter family doctor phone',
        page: 2,
        heading: 'Number',
      },
      {
        id: generateUniqueId(),
        qt: 'Family Doctor Email',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'false',
        required: false,
        placeholder: 'Enter family doctor email',
        page: 2,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Referring Professional',
        icon: 'TfiText',
        title: 'Short answer',
        type: 'Textbox',
        default: 'false',
        required: false,
        placeholder: 'Enter referring professional name',
        page: 2,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Referring Professional Phone',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        default: 'false',
        required: false,
        placeholder: 'Enter referring professional phone',
        page: 2,
        heading: 'Number',
      },
      {
        id: generateUniqueId(),
        qt: 'Referring Professional Email',
        icon: 'TfiText',
        type: 'Textbox',
        default: 'false',
        title: 'Short answer',
        required: false,
        placeholder: 'Enter referring professional email',
        page: 2,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'How did you hear about us?',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        page: 2,
        heading: 'Multiple choice',
        default: 'false',
        required: false,
        options: [
          'Doctor Referral',
          'Friend/Family',
          'Online Search',
          'Social Media',
          'Other',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'Who were you referred to?',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'false',
        required: false,
        placeholder: 'Enter name of specific provider if applicable',
        page: 2,
        heading: 'Short answer',
      },

      {
        id: generateUniqueId(),
        qt: 'What are your concerns? (check all that apply)',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        page: 3,
        heading: 'Multiple choice',
        default: 'false',
        required: true,
        options: [
          'Speech/Articulation (saying words clearly)',
          'Expressive Language (getting words out)',
          'Receptive Language (understanding language)',
          'Feeding/Eating & Swallowing',
          'Literacy',
          'Cognitive-Communication (problem solving, attention, executive functions)',
          'Developmental milestones',
          'AAC (alternative methods to communicate - communication devices, PECS, communication boards, etc.,)',
          'Other',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'Please describe your concerns in more detail?',
        icon: 'TfiText',
        type: 'TextArea',
        page: 3,
        heading: 'Short answer',
        default: 'false',
        title: 'Paragraph',
        required: true,
      },
      {
        id: generateUniqueId(),
        qt: 'How does your child currently communicate with you?',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        default: 'false',
        page: 3,
        heading: 'Short answer',
        required: true,
      },
      {
        id: generateUniqueId(),
        qt: 'What activities or toys does your child enjoy playing with?',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 3,
        heading: 'Short answer',
        default: 'false',
        required: true,
      },
    ],
  },
  {
    description: 'Medical History ',
    title: 'Medical History Form',
    slug: 'medical-history-form',
    styles: {
      backgroundColor: '#fffff',
      textColor: '#212529',
      fontWeight: '400',
      formColor: '#FCF6E9',
      buttonAlignment: 'left',
      pageColor: '#4A148C',
      fontColor: '#723ffa',
      inputBackground: '#ffffff',
      fieldSpacing: '24px',
      fontSize: '16px',
      borderRadius: '8px',
      borderColor: '#dddddd',
      // New defaults
      buttonText: 'Submit Form',
      buttonColor: '#723ffa',
      buttonTextColor: '#ffffff',
      buttonSize: 'md',
      titleAlignment: 'left',
      titleSize: '24px',
    },
    image: '',
    questions: [
      {
        id: generateUniqueId(),
        qt: 'Email Address',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'true',
        required: true,
        placeholder: 'Enter your email',
        page: 1,
        heading: 'Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'First Name',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        page: 1,
        heading: 'Short answer',
        default: 'false',
        required: true,
        placeholder: 'Enter first name',
      },
      {
        id: generateUniqueId(),
        qt: 'Please describe your pregnancy and delivery including any challenges, birth weight, length of pregnancy and method of delivery.',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 1,
        heading: 'Short answer',
        default: 'false',
        required: false,
        placeholder: 'Enter your answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Does your child have a medical condition/diagnosis (i.e., Apraxia, Autism, Genetic Disorder, etc.,)? Please explain.',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 1,
        heading: 'Short answer',
        default: 'false',
        required: false,
        placeholder: 'Enter your answer',
      },

      {
        id: generateUniqueId(),
        qt: 'Please share any relevant medical history (illness, ear infections etc.)',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 1,
        heading: 'Short answer',
        default: 'false',
        required: false,
        placeholder: 'Enter your answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Does your child have any allergies?',
        icon: 'TfiText',
        type: 'Textbox',
        page: 1,
        heading: 'Short answer',
        title: 'Short answer',
        default: 'false',
        required: false,
        placeholder: 'Enter your answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Do you have a family history of...',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        page: 1,
        heading: 'Multiple choice',
        default: 'false',
        required: false,
        options: [
          'Speech and language delays',
          'Learning disability',
          'Hearing loss',
          'Stuttering',
          'None',
          'Other',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'Please specify.',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 1,
        heading: 'Short answer',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'Do you have any concerns regarding your child"s hearing ability?',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        page: 1,
        heading: ' Multiple choice',
        default: 'false',
        required: false,
        options: ['Yes', 'No', 'Not Sure'],
      },
      {
        id: generateUniqueId(),
        qt: 'Has your child had a hearing test?',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        page: 1,
        heading: ' Multiple choice',
        title: 'Multiple choice',
        default: 'false',
        required: false,
        options: ['Yes', 'No', 'On waiting list for hearing assessment'],
      },
      {
        id: generateUniqueId(),
        qt: 'Hearing test results(id applicable)',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 2,
        heading: 'Short answer',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'Is your child seeing another specialist?',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        page: 2,
        heading: 'Multiple choice',
        default: 'false',
        required: false,
        options: [
          'Speech-Language Pathologist',
          'Occupational Therapist',
          'Physiotherapist',
          'Family Doctor',
          'Pediatrician',
          'Infant Development Consultant',
          'Supported Child Development Consultant',
          'Other',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'Please list out any other specialist that your child is seeing.',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 2,
        heading: 'Short answer',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'Has your child seen another speech-language pathologist within the last year?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 2,
        heading: 'Single choice',
        default: 'false',
        required: false,
        options: ['Yes', 'No'],
      },
      {
        id: generateUniqueId(),
        qt: 'Has your child completed a speech-language assessment within the last year?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        page: 2,
        heading: 'Single choice',
        title: 'Single choice',
        default: 'false',
        required: false,
        options: ['Yes', 'No'],
      },
      {
        id: generateUniqueId(),
        qt: 'Has your child completed a speech-language assessment within the last year?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        default: 'false',
        page: 2,
        heading: 'Single choice',
        required: false,
        options: ['Yes', 'No'],
      },
      {
        id: generateUniqueId(),
        qt: 'Please describe any goal areas that you are currently working on with other therapist(s).',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 2,
        heading: 'Short answer',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'How old was your child when they started babbling? (baba, mama, etc.,)',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        default: 'false',
        page: 2,
        heading: 'Number',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'How old was your child when they said their first word?',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        page: 2,
        heading: 'Number',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'How old was your child when they started to point?',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        page: 2,
        heading: 'Number',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'How old was your child when they learned to crawl?',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        page: 3,
        heading: 'Number',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'How old was your child when they learned to walk?',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        page: 3,
        heading: 'Number',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: "Please share any additional information about your child's development, (fine motor skills, communication, grossmotor skills).",
        icon: 'TfiText',
        type: 'TextArea',
        page: 3,
        heading: 'Short answer',
        title: 'Paragraph',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'Does your child attend school?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 3,
        heading: 'Single choice',
        default: 'false',
        required: true,
        options: ['Yes', 'No'],
      },
      {
        id: generateUniqueId(),
        qt: "What is the name of your child's childcare facility or school?",
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        page: 3,
        heading: 'Multiple choice',
        default: 'false',
        required: true,
        options: ['Yes', 'No'],
      },
      {
        id: generateUniqueId(),
        qt: 'What community events does your child attend?',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        page: 3,
        heading: 'Multiple choice',
        default: 'false',
        required: false,
        options: [
          'Library Story Time',
          'Family Place',
          'Community Center Drop-Ins',
          'Sports',
          'Music Classes',
          'Gymnastics',
          'Other',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'Who does your child live with?',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        page: 3,
        heading: 'Multiple choice',
        default: 'false',
        required: false,
        options: [
          'Mother',
          'Father',
          'Sibling(s)',
          'Aunt',
          'Uncle',
          'Grandparent(s)',
          'Other',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'What languages are your child exposed and the percentage of the time your child hears these languages (e.g.,50% Mandarin, 50% English):',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 3,
        heading: ' Short answer',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'If your child is exposed to more than one language, could you describe when and who your child learns these  languages from (e.g., daycare teacher speaks English, grandparents speak Cantonese on the weekends)?',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 3,
        heading: ' Short answer',
        default: 'false',
        required: false,
      },
      {
        id: generateUniqueId(),
        qt: 'Is there anything that we missed that you would like us to know?',
        icon: 'TfiText',
        type: 'TextArea',
        title: 'Paragraph',
        page: 3,
        heading: ' Short answer',
        default: 'false',
        required: false,
      },
    ],
  },
  {
    description: 'Customer feedback collection form',
    title: 'Customer Feedback',
    slug: 'customer-feedback',
    styles: {
      backgroundColor: '#f0f8ff',
      textColor: '#333333',
      fontWeight: '400',
      buttonAlignment: 'left',
      formColor: '#ffffff',
      pageColor: '#4682b4',
      fontColor: '#2c3e50',
      inputBackground: '#f9f9f9',
      fieldSpacing: '20px',
      fontSize: '15px',
      borderRadius: '6px',
      borderColor: '#cccccc',
      // New defaults
      buttonText: 'Submit Feedback',
      buttonColor: '#2c3e50',
      buttonTextColor: '#ffffff',
      buttonSize: 'md',
      titleAlignment: 'left',
      titleSize: '22px',
    },
    image: '',
    questions: [
      {
        id: generateUniqueId(),
        qt: 'Email Address',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'true',
        required: true,
        placeholder: 'Enter your email',
        page: 1,
        heading: ' Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'First Name',
        icon: 'TfiText',
        title: 'Short answer',
        type: 'Textbox',
        default: 'false',
        required: true,
        placeholder: 'Enter first name',
        page: 1,
        heading: ' Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'How would you rate our service?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 1,
        heading: ' Single choice',
        default: 'false',
        required: true,
        options: ['1', '2', '3', '4', '5'],
      },
      {
        id: generateUniqueId(),
        qt: 'What aspects of our service did you like?',
        icon: 'IoIosCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        default: 'false',
        page: 1,
        heading: ' Multiple choice',
        required: false,
        options: [
          'Customer Support',
          'Product Quality',
          'Delivery Time',
          'Value for Money',
          'User Experience',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'How can we improve our service?',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        page: 1,
        heading: ' Short answer',
        default: 'false',
        required: false,
        placeholder: 'Please share your suggestions',
      },
      {
        id: generateUniqueId(),
        qt: 'Would you recommend our service to others?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 1,
        heading: ' Single choice',
        default: 'false',
        required: true,
        options: ['Yes, definitely', 'Maybe', 'No'],
      },
    ],
  },
  {
    description: 'Job application form for candidates',
    title: 'Job Application',
    slug: 'job-application',
    styles: {
      backgroundColor: '#f5f5f5',
      textColor: '#333333',
      fontWeight: '400',
      buttonAlignment: 'left',
      formColor: '#ffffff',
      pageColor: '#546e7a',
      fontColor: '#37474f',
      inputBackground: '#ffffff',
      fieldSpacing: '22px',
      fontSize: '15px',
      borderRadius: '4px',
      borderColor: '#e0e0e0',
      // New defaults
      buttonText: 'Submit Application',
      buttonColor: '#37474f',
      buttonTextColor: '#ffffff',
      buttonSize: 'md',
      titleAlignment: 'left',
      titleSize: '22px',
    },
    image: '',
    questions: [
      {
        id: generateUniqueId(),
        qt: 'Email Address',
        icon: 'TfiText',
        title: 'Short answer',
        type: 'Textbox',
        default: 'true',
        page: 1,
        heading: ' Short answer',
        required: true,
        placeholder: 'Enter your email',
      },
      {
        id: generateUniqueId(),
        qt: 'Full Name',
        icon: 'TfiText',
        title: 'Short answer',
        type: 'Textbox',
        default: 'false',
        page: 1,
        heading: ' Short answer',
        required: true,
        placeholder: 'Enter your full name',
      },

      {
        id: generateUniqueId(),
        qt: 'Phone Number',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        page: 1,
        heading: ' Number',
        default: 'false',
        required: true,
        placeholder: 'Enter your phone number',
      },
      {
        id: generateUniqueId(),
        qt: 'Position Applying For',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 1,
        heading: ' Single choice',
        default: 'false',
        required: true,
        options: [
          'Software Developer',
          'UX Designer',
          'Product Manager',
          'Data Analyst',
          'Marketing Specialist',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'Years of Experience',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 1,
        heading: ' Single choice',
        default: 'false',
        required: true,
        options: [
          'Less than 1 year',
          '1-3 years',
          '3-5 years',
          '5-10 years',
          'More than 10 years',
        ],
      },

      {
        id: generateUniqueId(),
        qt: 'Cover Letter',
        icon: 'TfiText',
        type: 'TextArea',
        page: 1,
        heading: ' Short answer',
        title: 'Paragraph',
        default: 'false',
        required: false,
        placeholder: 'Tell us why you would be a good fit for this position',
      },
    ],
  },

  {
    description: 'Survey form for product feedback',
    title: 'Product Survey',
    slug: 'product-survey',
    styles: {
      backgroundColor: '#f0fff4',
      textColor: '#2c3e50',
      fontWeight: '400',
      formColor: '#ffffff',
      pageColor: '#38a169',
      fontColor: '#276749',
      buttonAlignment: 'left',
      inputBackground: '#f7fafc',
      fieldSpacing: '20px',
      fontSize: '15px',
      borderRadius: '6px',
      borderColor: '#e2e8f0',
      // New defaults
      buttonText: 'Submit Survey',
      buttonColor: '#276749',
      buttonTextColor: '#ffffff',
      buttonSize: 'md',
      titleAlignment: 'left',
      titleSize: '22px',
    },
    image: '',
    questions: [
      {
        id: generateUniqueId(),
        qt: 'Email Address',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'true',
        required: true,
        placeholder: 'Enter your email',
        page: 1,
        heading: ' Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'How often do you use our product?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 1,
        heading: ' Single choice',
        default: 'false',
        required: true,
        options: ['Daily', 'Weekly', 'Monthly', 'Rarely', 'Never'],
      },
      {
        id: generateUniqueId(),
        qt: 'How satisfied are you with our product?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 1,
        heading: ' Single choice',
        default: 'false',
        required: true,
        options: ['1', '2', '3', '4', '5'],
      },

      {
        id: generateUniqueId(),
        qt: 'What improvements would you suggest?',
        icon: 'TfiText',
        type: 'Textbox',
        page: 1,
        heading: ' Short answer',
        title: 'Short answer',
        default: 'false',
        required: false,
        placeholder: 'Please share your suggestions',
      },
    ],
  },
  {
    description: 'Comprehensive life coach intake form',
    title: 'Life Coach Intake',
    slug: 'life-coach-intake',
    styles: {
      backgroundColor: '#f8f9fa',
      textColor: '#212529',
      fontWeight: '400',
      buttonAlignment: 'left',
      formColor: '#ffffff',
      pageColor: '#6c757d',
      fontColor: '#495057',
      inputBackground: '#f8f9fa',
      fieldSpacing: '20px',
      fontSize: '14px',
      borderRadius: '6px',
      borderColor: '#ced4da',
      // New defaults
      buttonText: 'Submit Form',
      buttonColor: '#495057',
      buttonTextColor: '#ffffff',
      buttonSize: 'md',
      titleAlignment: 'left',
      titleSize: '22px',
    },
    image: '',
    questions: [
      {
        id: generateUniqueId(),
        qt: 'Email Address',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        page: 1,
        heading: ' Short answer',
        default: 'true',
        required: true,
        placeholder: 'Enter your email',
      },
      {
        id: generateUniqueId(),
        qt: 'Full Name',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'false',
        page: 1,
        heading: ' Short answer',
        required: true,
        placeholder: 'Enter your full name',
      },

      {
        id: generateUniqueId(),
        qt: 'Phone Number',
        icon: 'GoHash',
        type: 'Number',
        title: 'Short answer',
        page: 1,
        heading: ' Number',
        default: 'false',
        required: true,
        placeholder: 'Enter your phone number',
      },
      {
        id: generateUniqueId(),
        qt: 'What are your top three goals for coaching?',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'false',
        page: 1,
        heading: ' Short answer',
        required: true,
        placeholder: 'Describe your goals in detail',
      },
      {
        id: generateUniqueId(),
        qt: 'Which areas of life would you like to focus on?',
        icon: 'IoMdCheckboxOutline',
        type: 'Multiple choice',
        title: 'Multiple choice',
        default: 'false',
        page: 1,
        heading: ' Multiple choice',
        required: true,
        options: [
          'Career',
          'Relationships',
          'Health',
          'Finance',
          'Personal Growth',
          'Spirituality',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'Have you worked with a coach before?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 1,
        heading: ' Single choice',
        default: 'false',
        required: true,
        options: ['Yes', 'No'],
      },
      {
        id: 7,
        qt: 'How did you hear about us?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 1,
        heading: ' Single choice',
        default: 'false',
        required: false,
        options: [
          'Word of mouth',
          'Social media',
          'Search engine',
          'Referral',
          'Other',
        ],
      },
    ],
  },

  {
    description: 'Basic country selection form',
    title: 'Country Selector',
    slug: 'country',
    styles: {
      backgroundColor: '#ffffff',
      textColor: '#000000',
      fontWeight: '400',
      formColor: '#FCF6E9',
      buttonAlignment: 'left',
      pageColor: '#E59244',
      fontColor: '#723ffa',
      inputBackground: '#fff',
      fieldSpacing: '24px',
      fontSize: '16px',
      borderRadius: '8px',
      borderColor: '#dddddd',
      // New defaults
      buttonText: 'Submit',
      buttonColor: '#723ffa',
      buttonTextColor: '#ffffff',
      buttonSize: 'md',
      titleAlignment: 'left',
      titleSize: '22px',
    },
    image: '',
    questions: [
      {
        id: generateUniqueId(),
        qt: 'Email Address',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'true',
        required: true,
        placeholder: 'Enter your email',
        page: 1,
        heading: ' Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Which country do you live in?',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        page: 1,
        heading: ' Single choice',
        title: 'Single choice',
        default: 'false',
        required: true,
        options: [
          'Canada',
          'United States',
          'UK',
          'Australia',
          'Germany',
          'France',
          'Japan',
          'Other',
        ],
      },
    ],
  },
  {
    description: 'Appointment booking form',
    title: 'Appointment Booking',
    slug: 'appointment-booking',
    styles: {
      backgroundColor: '#f5f5f5',
      textColor: '#333333',
      fontWeight: '400',
      formColor: '#ffffff',
      pageColor: '#009688',
      fontColor: '#00796b',
      inputBackground: '#ffffff',
      buttonAlignment: 'left',
      fieldSpacing: '20px',
      fontSize: '15px',
      borderRadius: '8px',
      borderColor: '#e0e0e0',
      // New defaults
      buttonText: 'Book Appointment',
      buttonColor: '#00796b',
      buttonTextColor: '#ffffff',
      buttonSize: 'md',
      titleAlignment: 'left',
      titleSize: '22px',
    },
    image: '',
    questions: [
      {
        id: generateUniqueId(),
        qt: 'Email Address',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'true',
        required: true,
        placeholder: 'Enter your email',
        page: 1,
        heading: ' Short answer',
      },
      {
        id: generateUniqueId(),
        qt: 'Full Name',
        icon: 'TfiText',
        type: 'Textbox',
        title: 'Short answer',
        default: 'false',
        required: true,
        placeholder: 'Enter your full name',
        page: 1,
        heading: ' Short answer',
      },

      {
        id: generateUniqueId(),
        qt: 'Phone Number',
        icon: 'GoHash',
        type: 'Number',
        title: 'Number',
        default: 'false',
        required: true,
        placeholder: 'Enter your phone number',
        page: 1,
        heading: ' Number',
      },
      {
        id: generateUniqueId(),
        qt: 'Preferred Date',
        icon: 'MdDateRange',
        title: 'Date',
        type: 'Date',
        page: 1,
        heading: 'Date',
        default: 'false',
        required: true,
      },
      {
        id: generateUniqueId(),
        qt: 'Preferred Time',
        icon: 'IoMdRadioButtonOn',
        type: 'Single choice',
        title: 'Single choice',
        page: 1,
        heading: ' Single choice',
        default: 'false',
        required: true,
        options: [
          'Morning (9am-12pm)',
          'Afternoon (1pm-5pm)',
          'Evening (6pm-8pm)',
        ],
      },
      {
        id: generateUniqueId(),
        qt: 'Reason for Appointment',
        icon: 'TfiText',
        page: 1,
        heading: ' Short answer',
        type: 'Textbox',
        default: 'false',
        required: false,
        placeholder: 'Please describe the reason for your appointment',
      },
    ],
  },
];
