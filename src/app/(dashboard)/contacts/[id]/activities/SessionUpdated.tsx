import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import React from 'react';
import { GoNote } from 'react-icons/go';

export default function SessionUpdated({ note, slp }: any) {
  return (
    <Flex maxW={'60rem'} flexWrap={'wrap'}>
      <Stack maxW={'3rem'} minW={'3rem'} gap={0} alignItems={'center'}>
        <GoNote size={'2rem'} color="#dee1e0" />
        <Box bg={'#dee1e0'} h={'100%'} w={'.5px'}></Box>
      </Stack>
      <Box
        rounded={'.3rem'}
        border={'1px solid #eef1f0'}
        py={'.8rem'}
        px={'.5rem'}
        alignItems={'flex-start'}
        w={'100%'}
        bg={'#f3f6f5'}
      >
        <Flex justifyContent={'space-between'}>
          <Text fontSize={'.9rem'} fontWeight={500}>
            {slp}
          </Text>
          <Text ml={'2rem'} fontSize={'.75rem'} color="#7C7C7C">
            {/* {moment(date).fromNow()} */}
          </Text>
        </Flex>
        <Box
          mt={'.2rem'}
          fontSize={'.75rem'}
          className={'noteBox'}
          dangerouslySetInnerHTML={{ __html: note }}
        ></Box>
      </Box>
    </Flex>
  );
}
