import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

type Params = {
  since_date: string;
};

async function getInvoiceHoursStats(params: Params) {
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  const url = new URL('/api/overview/invoiced-hours', window.location.origin);
  url.searchParams.set('since_date', params.since_date);

  if (!org?.id) {
    throw new Error('Missing organization ID');
  }

  url.searchParams.set('org_id', org?.id.toString());

  const response = await fetch(url.toString(), {
    method: 'GET',
  });

  const json = await response.json();

  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch invoice stats');
  }

  return json;
}

type QueryFnType = typeof getInvoiceHoursStats;

export const useGetInvoiceHoursStatsQuery = (
  params: Params,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    queryKey: [queryKey.overview.invoiceStats, params],
    queryFn: () => getInvoiceHoursStats(params),
    ...config,
  });
};
