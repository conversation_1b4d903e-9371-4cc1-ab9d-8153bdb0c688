export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      bookings: {
        Row: {
          appointment: string | null;
          appointment_raw: string | null;
          assigned_to: string | null;
          booking_created_at_raw: string | null;
          calendly_event_type: string | null;
          client_id: number | null;
          created_at: string | null;
          created_dt: string | null;
          email: string | null;
          event: string | null;
          first_name: string | null;
          id: number;
          product_id: number;
          last_name: string | null;
          phone: string | null;
          product: string | null;
          province: string | null;
          referral: string | null;
          slp_id: number | null;
          emails: any;
          client_emails: any;
          utm_campaign: string | null;
          utm_content: string | null;
          utm_medium: string | null;
          utm_source: string | null;
        };
        Insert: {
          appointment?: string | null;
          appointment_raw?: string | null;
          assigned_to?: string | null;
          booking_created_at_raw?: string | null;
          calendly_event_type?: string | null;
          client_id?: number | null;
          created_at?: string | null;
          created_dt?: string | null;
          email?: string | null;
          event?: string | null;
          first_name?: string | null;
          id?: number;
          product_id: number;
          last_name?: string | null;
          phone?: string | null;
          product?: string | null;
          province?: string | null;
          referral?: string | null;
          slp_id?: number | null;
          utm_campaign?: string | null;
          utm_content?: string | null;
          utm_medium?: string | null;
          utm_source?: string | null;
        };
        Update: {
          appointment?: string | null;
          appointment_raw?: string | null;
          assigned_to?: string | null;
          booking_created_at_raw?: string | null;
          calendly_event_type?: string | null;
          client_id?: number | null;
          created_at?: string | null;
          created_dt?: string | null;
          email?: string | null;
          event?: string | null;
          first_name?: string | null;
          id?: number;
          product_id: number;
          last_name?: string | null;
          phone?: string | null;
          product?: string | null;
          province?: string | null;
          referral?: string | null;
          slp_id?: number | null;
          utm_campaign?: string | null;
          utm_content?: string | null;
          utm_medium?: string | null;
          utm_source?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'bookings_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'public_bookings_slp_id_fkey';
            columns: ['slp_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'bookings_product_id_fkey';
            columns: ['product_id'];
            isOneToOne: false;
            referencedRelation: 'products';
            referencedColumns: ['id'];
          },
        ];
      };
      template: {
        Row: {
          id: number | null;
          name: string | null;
          content: string | null;
          organization_id: number | null;
          replacement_tags: string | null;
          created_at: Date | null;
          updated_at: Date | null;
        };
        Insert: {
          name: string | null;
          content: string | null;
          organization_id: number | null;
          replacement_tags: string | null;
        };
        Update: {
          name?: string | null;
          content?: string | null;
          replacement_tags?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_org';
            columns: ['organization_id'];
            isOneToOne: false;
            referencedRelation: 'organizations';
            referencedColumns: ['id'];
          },
        ];
      };
      notes: {
        Row: {
          id: number | null;
          notes: string | null;
          title: string | null;
          status: string | null;
          client_id: number | null;
          organization_id: number | null;
          author_id: number | null;
          note_date: Date | null;
          created_at: Date | null;
          updated_at: Date | null;
        };
        Insert: {
          notes: string | null;
          title: string | null;
          status: string | null;
          client_id: number | null;
          organization_id: number | null;
          author_id: number | null;
          note_date: Date | null;
        };
        Update: {
          title: string | null;
          notes: string | null;
          status: string | null;
          note_date: Date | null;
        };
        Relationships: [
          {
            foreignKeyName: 'client_notes_organization_id_fkey';
            columns: ['organization_id'];
            isOneToOne: false;
            referencedRelation: 'organizations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'client_notes_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'notes_author_id_fkey';
            columns: ['author_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      forms: {
        Row: {
          id?: number | null;
          created_at?: string | null;
          organization_id?: number | null;
          updated_at?: string | null;
          product?: string | null;
          name?: string | null;
          description?: string | null;
          duration?: number | string | null;
          url?: string | null;
          success_message?: string | null;
        };
      };
      products: {
        Row: {
          id?: number | null;
          created_at?: string | null;
          price?: number | null;
          minutes?: number | null;
          status?: string | null;
          description?: string | null;
          name?: string | null;
        };
        Insert: {
          price?: number | null;
          id?: number | null;
          created_at?: string | null;
          minutes?: number | null;
          status?: string | null;
          description?: string | null;
          name?: string | null;
        };
        Update: {
          price?: number | null;
          minutes?: number | null;
          id?: number | null;
          created_at?: string | null;
          status?: string | null;
          description?: string | null;
          name?: string | null;
        };
      };
      package_list: {
        Row: {
          id?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
          package_name?: string | null;
          description?: string | null;
          price?: number | null;
          tax?: number | null;
          amount?: number | null;
          discount?: number | null;
          expiry_date?: string | null;
          organization_id?: number | null;
        };
        Insert: {
          id?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
          package_name?: string | null;
          description?: string | null;
          price?: number | null;
          tax?: number | null;
          discount?: number | null;
          amount?: number | null;
          expiry_date?: string | null;
          organization_id?: number | null;
        };
        Update: {
          updated_at?: string | null;
          package_name?: string | null;
          description?: string | null;
          price?: number | null;
          tax?: number | null;
          amount?: number | null;
          discount?: number | null;
          expiry_date?: string | null;
          organization_id?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'package_list_organization_id_fkey';
            columns: ['organization_id'];
            isOneToOne: false;
            referencedRelation: 'organizations';
            referencedColumns: ['id'];
          },
        ];
      };
      package_products: {
        Row: {
          id?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
          product_id?: number | null;
          package_id?: number | null;
          quantity?: number | null;
          client_id?: number | null;
          organization_id?: number | null;
        };
        Insert: {
          id?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
          product_id: number | null;
          package_id: number | null;
          quantity: number | null;
          client_id?: number | null;
          organization_id?: number | null;
        };
        Update: {
          id?: number | null;
          updated_at?: string | null;
          product_id?: number | null;
          package_id?: number | null;
          quantity?: number | null;
          client_id?: number | null;
          organization_id?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'package_products_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'package_products_organization_id_fkey';
            columns: ['organization_id'];
            isOneToOne: false;
            referencedRelation: 'organizations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'package_products_package_id_fkey';
            columns: ['package_id'];
            isOneToOne: false;
            referencedRelation: 'packages';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'package_products_product_id_fkey';
            columns: ['product_id'];
            isOneToOne: false;
            referencedRelation: 'products';
            referencedColumns: ['id'];
          },
        ];
      };
      client_emails: {
        Row: {
          client_id: number;
          created_at: string | null;
          email: string;
          id: number;
          is_primary_email: boolean;
        };
        Insert: {
          client_id: number;
          created_at?: string | null;
          email: string;
          id?: number;
          is_primary_email?: boolean;
        };
        Update: {
          client_id?: number;
          created_at?: string | null;
          email?: string;
          id?: number;
          is_primary_email?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: 'client_emails_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
        ];
      };
      clients: {
        Row: {
          affiliate: string | null;
          attribution: string | null;
          ax_date: string | null;
          company: string | null;
          consulted_by: string | null;
          coverage: string | null;
          first_name: string | null;
          first_tx: string | null;
          display_name: string | null;
          fu_call: string | null;
          fu_email: string | null;
          goals: string[] | null;
          id: number;
          email: string;
          insurance_company: string | null;
          Interest: string | null;
          job_title: string | null;
          last_called: string | null;
          last_consultation_date: string | null;
          last_emailed: string | null;
          last_name: string | null;
          lead_created: string | null;
          lead_quality: string | null;
          ltv: string | null;
          middle_name: string | null;
          notes: string | null;
          objection: string | null;
          phone: string | null;
          province: string | null;
          province_old: string | null;
          referral_source: string | null;
          referred_by: number | null;
          slp: string | null;
          slp_notes: string | null;
          source: string | null;
          stage: string | null;
          stripe: string | null;
          utm_campaign: string | null;
          utm_content: string | null;
          utm_medium: string | null;
          utm_source: string | null;
          waitlist: string | null;
          full_name: string | null;
          status: string | null;
        };
        Insert: {
          affiliate?: string | null;
          attribution?: string | null;
          ax_date?: string | null;
          company?: string | null;
          consulted_by?: string | null;
          coverage?: string | null;
          first_name?: string | null;
          first_tx?: string | null;
          fu_call?: string | null;
          fu_email?: string | null;
          goals?: string[] | null;
          id?: number;
          email: string;
          insurance_company?: string | null;
          Interest?: string | null;
          job_title?: string | null;
          last_called?: string | null;
          last_consultation_date?: string | null;
          last_emailed?: string | null;
          last_name?: string | null;
          lead_created?: string | null;
          lead_quality?: string | null;
          ltv?: string | null;
          middle_name?: string | null;
          notes?: string | null;
          objection?: string | null;
          phone?: string | null;
          province?: string | null;
          province_old?: string | null;
          referral_source?: string | null;
          referred_by?: number | null;
          slp?: string | null;
          slp_notes?: string | null;
          source?: string | null;
          stage?: string | null;
          stripe?: string | null;
          utm_campaign?: string | null;
          utm_content?: string | null;
          utm_medium?: string | null;
          utm_source?: string | null;
          waitlist?: string | null;
        };
        Update: {
          affiliate?: string | null;
          attribution?: string | null;
          ax_date?: string | null;
          company?: string | null;
          consulted_by?: string | null;
          coverage?: string | null;
          first_name?: string | null;
          first_tx?: string | null;
          fu_call?: string | null;
          fu_email?: string | null;
          goals?: string[] | null;
          id?: number;
          email?: string;
          insurance_company?: string | null;
          Interest?: string | null;
          job_title?: string | null;
          last_called?: string | null;
          last_consultation_date?: string | null;
          last_emailed?: string | null;
          last_name?: string | null;
          lead_created?: string | null;
          lead_quality?: string | null;
          ltv?: string | null;
          middle_name?: string | null;
          notes?: string | null;
          objection?: string | null;
          phone?: string | null;
          province?: string | null;
          province_old?: string | null;
          referral_source?: string | null;
          referred_by?: number | null;
          slp?: string | null;
          slp_notes?: string | null;
          source?: string | null;
          stage?: string | null;
          stripe?: string | null;
          utm_campaign?: string | null;
          utm_content?: string | null;
          utm_medium?: string | null;
          utm_source?: string | null;
          waitlist?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'clients_referred_by_fkey';
            columns: ['referred_by'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
        ];
      };
      notable_dates: {
        Row: {
          id: number;
          client_id: number | null;
          created_at: string | null;
          date: string | null;
          event: string | null;
          organization_id: number;
        };
        Insert: {
          id?: number;
          client_id: number | null;
          date: string | null;
          event: string | null;
          organization_id: number;
        };
        Update: {
          date?: string | null;
          event?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'notable_date_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'notable_date_organization_id_fkey';
            columns: ['organization_id'];
            isOneToOne: false;
            referencedRelation: 'organizations';
            referencedColumns: ['id'];
          },
        ];
      };
      followups: {
        Row: {
          client_id: number | null;
          contact_by: string | null;
          created_at: string | null;
          followup_date: string | null;
          id: number;
          status: string | null;
        };
        Insert: {
          client_id?: number | null;
          contact_by?: string | null;
          created_at?: string | null;
          followup_date?: string | null;
          id?: number;
          status?: string | null;
        };
        Update: {
          client_id?: number | null;
          contact_by?: string | null;
          created_at?: string | null;
          followup_date?: string | null;
          id?: number;
          status?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'followups_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
        ];
      };
      invoices: {
        Row: {
          change_reason: string | null;
          client_id: number | null;
          created_dt: string | null;
          data_source: string | null;
          duration: string | null;
          email: string | null;
          id: number;
          interval: number | null;
          invoice_date: string | null;
          invoice_date_raw: string | null;
          invoice_number: string | null;
          late_cancellation: string | null;
          linked_client_id: number | null;
          memo: string | null;
          internal_memo: string | null;
          name: string | null;
          package: string | null;
          package_id: number | null;
          package_size: string | null;
          product: string | null;
          qty: string | null;
          referral: string | null;
          registration: string | null;
          session_type: string | null;
          slp: string | null;
          slp_id: number | null;
          status: string | null;
          total_hours: number | null;
          total_price: number | null;
          slp_note_id: number | null;
          services?: {
            name?: string;
          };
        };
        Insert: {
          change_reason?: string | null;
          client_id?: number | null;
          created_dt?: string | null;
          data_source?: string | null;
          duration?: string | null;
          email?: string | null;
          id?: number;
          interval?: number | null;
          invoice_date?: string | null;
          invoice_date_raw?: string | null;
          invoice_number?: string | null;
          late_cancellation?: string | null;
          linked_client_id?: number | null;
          memo?: string | null;
          internal_memo?: string | null;
          name?: string | null;
          package?: string | null;
          package_id?: number | null;
          package_size?: string | null;
          product?: string | null;
          qty?: string | null;
          referral?: string | null;
          registration?: string | null;
          session_type?: string | null;
          slp?: string | null;
          slp_id?: number | null;
          status?: string | null;
          total_hours?: number | null;
          total_price?: number | null;
          slp_note_id?: number | null;
        };
        Update: {
          change_reason?: string | null;
          client_id?: number | null;
          created_dt?: string | null;
          data_source?: string | null;
          duration?: string | null;
          email?: string | null;
          id?: number;
          interval?: number | null;
          invoice_date?: string | null;
          invoice_date_raw?: string | null;
          invoice_number?: string | null;
          late_cancellation?: string | null;
          linked_client_id?: number | null;
          memo?: string | null;
          internal_memo?: string | null;
          name?: string | null;
          package?: string | null;
          package_id?: number | null;
          package_size?: string | null;
          product?: string | null;
          qty?: string | null;
          referral?: string | null;
          registration?: string | null;
          session_type?: string | null;
          slp?: string | null;
          slp_id?: number | null;
          status?: string | null;
          total_hours?: number | null;
          total_price?: number | null;
          slp_note_id?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'invoices_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'invoices_linked_client_id_fkey';
            columns: ['linked_client_id'];
            isOneToOne: false;
            referencedRelation: 'linked_clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'invoices_package_id_fkey';
            columns: ['package_id'];
            isOneToOne: false;
            referencedRelation: 'packages';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'public_invoices_slp_id_fkey';
            columns: ['slp_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      linked_clients: {
        Row: {
          client_id: number;
          created_at: string;
          first_name: string | null;
          id: number;
          last_name: string | null;
          relationship: string | null;
        };
        Insert: {
          client_id: number;
          created_at?: string;
          first_name?: string | null;
          id?: number;
          last_name?: string | null;
          relationship?: string | null;
        };
        Update: {
          client_id?: number;
          created_at?: string;
          first_name?: string | null;
          id?: number;
          last_name?: string | null;
          relationship?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'linked_client_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
        ];
      };
      packages: {
        Row: {
          balance: number | null;
          client_id: number | null;
          created_at: string;
          email: string | null;
          id: number;
          name: string | null;
          package_size: number | null;
          product: string | null;
          status: string | null;
          stripe_id: string | null;
          total: string | null;
          transaction_dt: string | null;
        };
        Insert: {
          balance?: number | null;
          client_id?: number | null;
          created_at?: string;
          email?: string | null;
          id?: number;
          name?: string | null;
          package_size?: number | null;
          product?: string | null;
          status?: string | null;
          stripe_id?: string | null;
          total?: string | null;
          transaction_dt?: string | null;
        };
        Update: {
          balance?: number | null;
          client_id?: number | null;
          created_at?: string;
          email?: string | null;
          id?: number;
          name?: string | null;
          package_size?: number | null;
          product?: string | null;
          status?: string | null;
          stripe_id?: string | null;
          total?: string | null;
          transaction_dt?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'packages_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
        ];
      };
      pay_definitions: {
        Row: {
          created_at: string;
          end_date: string | null;
          id: number;
          pay_period: string | null;
          pay_rate: number | null;
          slp_id: number | null;
          start_date: string | null;
        };
        Insert: {
          created_at?: string;
          end_date?: string | null;
          id?: number;
          pay_period?: string | null;
          pay_rate?: number | null;
          slp_id?: number | null;
          start_date?: string | null;
        };
        Update: {
          created_at?: string;
          end_date?: string | null;
          id?: number;
          pay_period?: string | null;
          pay_rate?: number | null;
          slp_id?: number | null;
          start_date?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'public_pay_definitions_slp_id_fkey';
            columns: ['slp_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      pay_schedules: {
        Row: {
          assesments: number | null;
          ax_pay: number | null;
          base_pay: number | null;
          created_at: string;
          date: string | null;
          id: number;
          pay_period: string | null;
          pay_period_raw: string | null;
          pay_rate: number | null;
          slp_id: number | null;
          status: string | null;
          total_hours: number | null;
          total_pay: number | null;
          year: string | null;
        };
        Insert: {
          assesments?: number | null;
          ax_pay?: number | null;
          base_pay?: number | null;
          created_at?: string;
          date?: string | null;
          id?: number;
          pay_period?: string | null;
          pay_period_raw?: string | null;
          pay_rate?: number | null;
          slp_id?: number | null;
          status?: string | null;
          total_hours?: number | null;
          total_pay?: number | null;
          year?: string | null;
        };
        Update: {
          assesments?: number | null;
          ax_pay?: number | null;
          base_pay?: number | null;
          created_at?: string;
          date?: string | null;
          id?: number;
          pay_period?: string | null;
          pay_period_raw?: string | null;
          pay_rate?: number | null;
          slp_id?: number | null;
          status?: string | null;
          total_hours?: number | null;
          total_pay?: number | null;
          year?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'public_pay_schedules_slp_id_fkey';
            columns: ['slp_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      referrals: {
        Row: {
          created_at: string;
          id: number;
          referee_id: number | null;
          referee_invoice: number | null;
          referrer_id: number | null;
          referrer_invoice: number | null;
        };
        Insert: {
          created_at?: string;
          id?: number;
          referee_id?: number | null;
          referee_invoice?: number | null;
          referrer_id?: number | null;
          referrer_invoice?: number | null;
        };
        Update: {
          created_at?: string;
          id?: number;
          referee_id?: number | null;
          referee_invoice?: number | null;
          referrer_id?: number | null;
          referrer_invoice?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'referrals_referee_id_fkey';
            columns: ['referee_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'referrals_referee_invoice_fkey';
            columns: ['referee_invoice'];
            isOneToOne: false;
            referencedRelation: 'invoices';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'referrals_referrer_id_fkey';
            columns: ['referrer_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'referrals_referrer_invoice_fkey';
            columns: ['referrer_invoice'];
            isOneToOne: false;
            referencedRelation: 'invoices';
            referencedColumns: ['id'];
          },
        ];
      };
      slp_notes: {
        Row: {
          booking_id: number;
          client_id: number;
          created_at: string;
          duration: string | null;
          id: number;
          invoice_id: number | null;
          invoice_memo: string | null;
          linked_client_id: number | null;
          no_show: string | null;
          package_id: number | null;
          referral: string | null;
          slp_id: number;
          soap_note: string | null;
          split_ax: string | null;
          status: string;
          note_date: string | null;
        };
        Insert: {
          booking_id: number;
          client_id: number;
          created_at?: string;
          duration?: string | null;
          id?: number;
          invoice_id?: number | null;
          invoice_memo?: string | null;
          linked_client_id?: number | null;
          no_show?: string | null;
          package_id?: number | null;
          referral?: string | null;
          slp_id: number;
          soap_note?: string | null;
          split_ax?: string | null;
          status?: string;
          note_date: string | null;
        };
        Update: {
          booking_id?: number;
          client_id?: number;
          created_at?: string;
          duration?: string | null;
          id?: number;
          invoice_id?: number | null;
          invoice_memo?: string | null;
          linked_client_id?: number | null;
          no_show?: string | null;
          package_id?: number | null;
          referral?: string | null;
          slp_id?: number;
          soap_note?: string | null;
          split_ax?: string | null;
          status?: string;
          note_date: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'public_slp_notes_booking_id_fkey';
            columns: ['booking_id'];
            isOneToOne: true;
            referencedRelation: 'bookings';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'public_slp_notes_slp_id_fkey';
            columns: ['slp_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'slp_notes_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'slp_notes_invoice_id_fkey';
            columns: ['invoice_id'];
            isOneToOne: false;
            referencedRelation: 'invoices';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'slp_notes_linked_client_id_fkey';
            columns: ['linked_client_id'];
            isOneToOne: false;
            referencedRelation: 'linked_clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'slp_notes_package_id_fkey';
            columns: ['package_id'];
            isOneToOne: false;
            referencedRelation: 'packages';
            referencedColumns: ['id'];
          },
        ];
      };
      user_permissions: {
        Row: {
          created_at: string;
          description: string | null;
          id: number;
          name: string | null;
          user_id: number | null;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          id?: number;
          name?: string | null;
          user_id?: number | null;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          id?: number;
          name?: string | null;
          user_id?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'public_user_permissions_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      users: {
        Row: {
          ax_comp_enabled: boolean | null;
          created_at: string | null;
          email: string | null;
          first_name: string | null;
          id: number;
          last_name: string | null;
          pay_rate: number | null;
          registration: string | null;
          role: string | null;
          status: string | null;
          timezone: string;
          permissions_id: string[];
        };
        Insert: {
          ax_comp_enabled?: boolean | null;
          created_at?: string | null;
          email?: string | null;
          first_name?: string | null;
          id?: number;
          last_name?: string | null;
          pay_rate?: number | null;
          registration?: string | null;
          role?: string | null;
          status?: string | null;
        };
        Update: {
          ax_comp_enabled?: boolean | null;
          created_at?: string | null;
          email?: string | null;
          first_name?: string | null;
          id?: number;
          last_name?: string | null;
          pay_rate?: number | null;
          registration?: string | null;
          role?: string | null;
          status?: string | null;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      contact_search: {
        Args: {
          search_input: string;
        };
        Returns: {
          id: number;
          first_name: string;
          last_name: string;
          phone: string;
          email: string;
          province: string;
          stage: string;
          lead_created: string;
          search: string;
        }[];
      };

      fetch_slp_clients_with_invoices: {
        Args: {
          slp_id: number;
          org_id: number;
          start_idx: number;
          end_idx: number;
        };
        Returns: {
          email: string | null;
          first_name: string | null;
          last_name: string | null;
          phone: string | null;
        }[];
      };
      get_services_with_taxes: {
        Args: {
          org_id: number;
          start: number;
          end: number;
        };
        Returns: {
          id: number;
          name: string | null;
          description: string | null;
          price: number | null;
          duration: number | null;
          taxes: any[];
        }[];
      };
      get_consultant_stage_breakdown: {
        Args: {
          org_id: number;
          start_date?: string;
        };
        Returns: {
          consultant: string;
          total: number;
          stages: Record<
            string,
            {
              count: number;
              percentage: number;
            }
          >;
        }[];
      };
      full_name: {
        Args: {
          '': unknown;
        };
        Returns: string;
      };
      search_clients: {
        Args: {
          search_text: string | null;
          provinces: string[] | null;
          stages: string[] | null;
          lead: string[] | null;
          page_limit: number;
          slp_values: number[] | null;
          page_offset: number;
        };
        Returns: Json;
      };
      search_test: {
        Args: {
          search_text: string | null;
          provinces: string[] | null;
          stages: string[] | null;
          lead: string[] | null;
          page_limit: number;
          slp_values: number[] | null;
          page_offset: number;
          active_client: boolean | null;
        };
        Returns: Json;
      };
      fetch_unlinked_invoices_two: {
        Args: {
          p_client_id: number;
        };
        Returns: Json;
      };
      get_packages: {
        Args: {
          formatted_search: string | null;
          package_status: string | null;
          start_index: number | null;
          end_index: number | null;
        };
        Returns: Json;
      };
      get_test_packages: {
        Args: {
          formatted_search: string | null;
          package_status: string | null;
          start_index: number | null;
          end_index: number | null;
          show_no_invoice: boolean | null;
        };
        Returns: Json;
      };
      search_clients_real: {
        Args: {
          search_text: string | null;
          provinces: string[] | null;
          stages: string[] | null;
          lead: string[] | null;
          goal: string[] | null;
          page_limit: number;
          slp_values: number[] | null;
          page_offset: number;
        };
        Returns: Json;
      };
      search_clients_filter: {
        Args: {
          search_text: string;
          provinces: string[];
          stages: string[];
          lead: string[];
          slp_values: number[];
          page_limit: number;
          page_offset: number;
        };
        Returns: Json;
      };
      slp_id_view: {
        Args: {
          slp_input: number;
        };
        Returns: {
          first_name: string;
          last_name: string;
          province: string;
          email: string;
          client_id: number;
          slp: string;
          memo: string;
          total_sessions: number;
          total_spent: number;
          total_hours: number;
          ax_count: number;
          tx_count: number;
          last_session_date: string;
          slp_notes: string;
        }[];
      };
      slp_overview: {
        Args: Record<PropertyKey, never>;
        Returns: {
          slps_id: string;
          slp_status: string;
          registration: string;
          first_name: string;
          last_name: string;
          unique_clients: number;
          total_hours: number;
          total_sessions: number;
          avg_ltv: number;
          repeat_clients: number;
        }[];
      };
      slp_view: {
        Args: {
          slp_input: number;
        };
        Returns: {
          first_name: string;
          last_name: string;
          province: string;
          client_id: number;
          email: string;
          slp: string;
          total_sessions: number;
          total_spent: number;
          total_hours: number;
          ax_count: number;
          tx_count: number;
          last_session_date: string;
        }[];
      };
      slp_view2: {
        Args: {
          slp_input: number;
        };
        Returns: {
          first_name: string;
          last_name: string;
          province: string;
          client_id: number;
          email: string;
          slp: string;
          last_memo: string;
          total_sessions: number;
          total_spent: number;
          total_hours: number;
          ax_count: number;
          tx_count: number;
          last_session_date: string;
        }[];
      };
      slp_view3: {
        Args: {
          slp_input: number;
        };
        Returns: {
          first_name: string;
          last_name: string;
          province: string;
          client_id: number;
          email: string;
          slp: string;
          last_memo: string;
          total_sessions: number;
          total_spent: number;
          total_hours: number;
          ax_count: number;
          tx_count: number;
          last_session_date: string;
          slp_notes: string;
        }[];
      };
      update_primary_email: {
        Args: {
          id: number;
          email_id: string;
        };
        Returns: number;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}

type PublicSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
  PublicTableNameOrOptions extends
  | keyof (PublicSchema['Tables'] & PublicSchema['Views'])
  | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
  ? keyof (Database[PublicTableNameOrOptions['schema']]['Tables'] &
    Database[PublicTableNameOrOptions['schema']]['Views'])
  : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions['schema']]['Tables'] &
    Database[PublicTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
  ? R
  : never
  : PublicTableNameOrOptions extends keyof (PublicSchema['Tables'] &
    PublicSchema['Views'])
  ? (PublicSchema['Tables'] &
    PublicSchema['Views'])[PublicTableNameOrOptions] extends {
      Row: infer R;
    }
  ? R
  : never
  : never;

export type TablesInsert<
  PublicTableNameOrOptions extends
  | keyof PublicSchema['Tables']
  | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
  ? keyof Database[PublicTableNameOrOptions['schema']]['Tables']
  : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions['schema']]['Tables'][TableName] extends {
    Insert: infer I;
  }
  ? I
  : never
  : PublicTableNameOrOptions extends keyof PublicSchema['Tables']
  ? PublicSchema['Tables'][PublicTableNameOrOptions] extends {
    Insert: infer I;
  }
  ? I
  : never
  : never;

export type TablesUpdate<
  PublicTableNameOrOptions extends
  | keyof PublicSchema['Tables']
  | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
  ? keyof Database[PublicTableNameOrOptions['schema']]['Tables']
  : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions['schema']]['Tables'][TableName] extends {
    Update: infer U;
  }
  ? U
  : never
  : PublicTableNameOrOptions extends keyof PublicSchema['Tables']
  ? PublicSchema['Tables'][PublicTableNameOrOptions] extends {
    Update: infer U;
  }
  ? U
  : never
  : never;

export type Enums<
  PublicEnumNameOrOptions extends
  | keyof PublicSchema['Enums']
  | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
  ? keyof Database[PublicEnumNameOrOptions['schema']]['Enums']
  : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions['schema']]['Enums'][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema['Enums']
  ? PublicSchema['Enums'][PublicEnumNameOrOptions]
  : never;
