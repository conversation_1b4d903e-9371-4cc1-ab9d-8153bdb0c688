'use client';
import CustomDatePicker from '@/components/elements/date-picker/date-picker';
//import TextEditor from '@/components/Input/CustomEditor';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import {
  contactChannelsOptions,
  leadQualitiesOptions,
  provinceOptions,
  referralSourceOptions,
} from '@/data/options/consultations';
import { ContactStageOption } from '@/hooks/clients/useGetContactsStages';
import { useAddConsultationHook } from '@/hooks/receptionist/consultations/useAddConsultationHook';
import {
  Box,
  Card,
  chakra,
  GridItem,
  HStack,
  Icon,
  IconButton,
  SimpleGrid,
  Skeleton,
  Text,
  VStack,
} from '@chakra-ui/react';
import truncate from 'html-truncate';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { FiCalendar, FiFileText, FiPlus } from 'react-icons/fi';
import CommunicationGoals from './communication-goals';
import TextEditorNew from '@/components/Input/NewTextEditor';
import {
  AccordionItem,
  AccordionItemContent,
  AccordionItemTrigger,
  AccordionRoot,
} from '@/components/ui/accordion';
import Link from 'next/link';

interface AddConsultationProps {
  row: any;
  onClose: () => void;
  contactStagesOptions: ContactStageOption[];
}

export default function AddConsultation({
  row,
  contactStagesOptions,
  onClose,
}: AddConsultationProps) {
  const {
    stageIsSql,
    errors,
    handleChange,
    touched,
    handleBlur,
    setFieldValue,
    values,
    addFollowUp,
    handleFormSubmit,
    notesLoading,
    AFloading,
    saveClientLoading,
    initialNotesFromDB,
  } = useAddConsultationHook({ row, onClose, handleAddNote });
  const [currentNewNote, setCurrentNewNote] = useState<string>('');
  const [displayNotes, setDisplayNotes] = useState<
    {
      id?: number;
      content: string;
      note_date: string;
      consulted_by: string;
      isExisting?: boolean;
    }[]
  >([]);
  const [clearCount, setClearCount] = useState(0);

  // Initialize display notes with existing notes from database
  useEffect(() => {
    if (initialNotesFromDB && initialNotesFromDB.length > 0) {
      const existingNotes = initialNotesFromDB.map((note: any) => ({
        id: note.id,
        content: note.notes, // Assuming the note content is in 'notes' field
        note_date: moment(note.note_date).format('DD MMM YYYY').toLowerCase(),
        consulted_by: note.consulted_by || 'Unknown',
        isExisting: true,
      }));
      setDisplayNotes(existingNotes);
    }
  }, [initialNotesFromDB]);

  // Remove the problematic useEffect that was overriding notes
  // useEffect(() => {
  //   setFieldValue('notes', currentNewNote ? [currentNewNote] : []);
  // }, [currentNewNote, setFieldValue]);

  const renderErrorText = (error?: unknown): React.ReactNode => {
    if (typeof error === 'string') {
      return error;
    }
    return null;
  };

  function handleAddNote() {
    if (currentNewNote.trim() !== '') {
      const newNoteEntry = {
        content: currentNewNote.trim(),
        note_date: moment().format('DD MMM YYYY').toLowerCase(),
        consulted_by: values.consulted_by || 'Current User',
        isExisting: false,
      };

      // Update display notes for UI (shows both existing and new notes)
      setDisplayNotes((prevNotes) => [...prevNotes, newNoteEntry]);

      // Update formik notes array with the new note content
      // Only add new notes to the formik values (not existing ones)
      const newNotesOnly = [...values.notes, newNoteEntry.content];
      setFieldValue('notes', newNotesOnly);

      // Clear the current input for next note
      setCurrentNewNote('');
      setClearCount((c) => c + 1);
    }
  }

  // console.log('currentNewNote', currentNewNote);

  // console.log('row', row);
  return (
    <Box maxW="800px" mx="auto" p={1}>
      {/* Header Section */}
      <VStack spaceY={2} mb={8} textAlign="center">
        <Icon as={FiFileText} boxSize={6} color="orange.500" />
        <Link href={`/contacts/${row?.clients?.id}`}>
          <Text fontWeight="600" fontSize="1.5rem" color="gray.800">
            {row?.clients?.display_name
              ? row.clients.display_name
              : `${row?.clients?.first_name || ''} ${row?.clients?.last_name || ''}`.trim()}
          </Text>
        </Link>
        <HStack spaceX={3} color="gray.600" fontSize="sm">
          <Text>{row?.email}</Text>
          <Text>•</Text>
          <Text> {row?.clients?.id}</Text>
        </HStack>
      </VStack>

      <chakra.form onSubmit={handleFormSubmit}>
        {/* Consultation Details Section */}
        <Card.Root size="sm" mb={6} variant="elevated">
          <Card.Body>
            <HStack mb={4} spaceX={2}>
              <Icon as={FiFileText} color="gray.600" />
              <Text fontWeight="600" fontSize="lg" color="gray.800">
                Consultation Details
              </Text>
            </HStack>

            <SimpleGrid gap={4} columns={{ base: 1, md: 2 }}>
              <GridItem>
                <CustomSelect
                  placeholder="Select status"
                  options={contactStagesOptions}
                  onChange={(val) => {
                    setFieldValue('stage', val.value);
                  }}
                  label="Status"
                  defaultValue={contactStagesOptions.find(
                    (item) =>
                      item.value.toLowerCase() === values.stage.toLowerCase()
                  )}
                />
              </GridItem>

              {stageIsSql && (
                <GridItem>
                  <CustomSelect
                    placeholder="Select lead quality"
                    options={leadQualitiesOptions}
                    onChange={(val) => {
                      setFieldValue('lead_quality', val.value);
                    }}
                    label="Lead Quality"
                    defaultValue={leadQualitiesOptions.find(
                      (item) =>
                        item.value.toLowerCase() ===
                        values.lead_quality.toLowerCase()
                    )}
                  />
                </GridItem>
              )}

              <GridItem>
                <CustomSelect
                  placeholder="Select province"
                  options={provinceOptions}
                  onChange={(val) => {
                    setFieldValue('province', val.value);
                  }}
                  label="Province"
                  defaultValue={provinceOptions.find(
                    (item) =>
                      item.value.toLowerCase() === values.province.toLowerCase()
                  )}
                />
              </GridItem>

              <GridItem>
                <StringInput
                  inputProps={{
                    name: 'consulted_by',
                    type: 'text',
                    onChange: handleChange,
                    value: values.consulted_by,
                    onBlur: handleBlur,
                  }}
                  fieldProps={{
                    label: 'Consulted By',
                    invalid: Boolean(
                      errors.consulted_by && touched.consulted_by
                    ),
                    errorText: renderErrorText(errors.consulted_by),
                  }}
                />
              </GridItem>

              <GridItem>
                <CustomSelect
                  placeholder="Select source"
                  options={referralSourceOptions}
                  onChange={(val) => {
                    setFieldValue('referral_source', val.value);
                  }}
                  label="Referral Source"
                  defaultValue={
                    referralSourceOptions.find(
                      (item) =>
                        item.value.toLowerCase() ===
                        values.referral_source.toLowerCase()
                    ) || referralSourceOptions[0]
                  }
                />
              </GridItem>

              <GridItem colSpan={2}>
                <CommunicationGoals
                  values={values}
                  setFieldValue={setFieldValue}
                />
              </GridItem>
            </SimpleGrid>
          </Card.Body>
        </Card.Root>

        {/* Consultation Notes Section */}
        <Card.Root p={'1rem'} size="sm" mb={6} variant="elevated">
          <AccordionRoot
            borderRadius={'.5rem'}
            border={'1px solid black'}
            // borderBottom={'1px solid black'}
            marginY={'1rem'}
            collapsible
          >
            <AccordionItem
              value={'picker'}
              border={'none'}
              boxShadow={'none'}
              alignItems={'center'}
            >
              <AccordionItemTrigger
                p={'2'}
                borderRadius="lg"
                _hover={{
                  bg: 'gray.50',
                }}
                width={'100%'}
                cursor={'pointer'}
              >
                <HStack spaceX={2}>
                  <Icon as={FiFileText} color="gray.600" />
                  <Text fontWeight="600" fontSize="lg" color="gray.800">
                    Consultation Notes
                  </Text>
                </HStack>
              </AccordionItemTrigger>
              <AccordionItemContent>
                <Box>
                  <Card.Body>
                    <VStack spaceY={3} align="stretch">
                      {notesLoading ? (
                        <>
                          {Array.from({ length: 2 }).map((_, index) => (
                            <Box
                              key={`note-${index}`}
                              p={3}
                              bg="blue.50"
                              borderRadius="md"
                              borderLeft="4px solid"
                              borderLeftColor="blue.400"
                              display={'flex'}
                              flexDirection={'column'}
                              gap={2}
                            >
                              <Skeleton
                                height={'12px'}
                                background="rgba(0, 0, 0, 0.1)"
                                opacity={0.7}
                                width="100%"
                                borderRadius="md"
                              />
                              <Skeleton
                                height={'8px'}
                                background="rgba(0, 0, 0, 0.1)"
                                opacity={0.7}
                                width="80px"
                                borderRadius="md"
                              />
                            </Box>
                          ))}
                        </>
                      ) : (
                        <>
                          {displayNotes.map((note, index) => (
                            <Box
                              key={`note-${note.id || index}`}
                              p={3}
                              bg={note.isExisting ? '#f0f9ff' : '#fdf2f8'}
                              borderRadius="md"
                              borderLeft="4px solid"
                              borderLeftColor={
                                note.isExisting ? 'blue.400' : 'primary.500'
                              }
                            >
                              <Text
                                maxWidth={'full'}
                                width={'full'}
                                truncate
                                whiteSpace={'nowrap'}
                                overflow={'hidden'}
                                textOverflow={'ellipsis'}
                                className="noteBox"
                                dangerouslySetInnerHTML={{
                                  __html: truncate(
                                    note?.content
                                      ?.replace(/\s+/g, ' ')
                                      .replace(/\n/g, ' '),
                                    60
                                  ),
                                }}
                              >
                                {/* {details?.notes} */}
                              </Text>
                              {/* <Box
                        fontSize="sm"
                        color="gray.700"
                        whiteSpace="pre-wrap"
                        letterSpacing={'0.5px'}
                        dangerouslySetInnerHTML={{ __html: note.content }}
                      ></Box> */}
                              <HStack justify="space-between" mt={2}>
                                <Text fontSize="xs" color="gray.500">
                                  {note.note_date}

                                  {values?.consulted_by &&
                                    ` . ${values?.consulted_by}`}
                                </Text>
                                {!note.isExisting && (
                                  <Text
                                    fontSize="xs"
                                    color="green.600"
                                    fontWeight="500"
                                  >
                                    New
                                  </Text>
                                )}
                              </HStack>
                            </Box>
                          ))}
                        </>
                      )}
                    </VStack>
                  </Card.Body>
                </Box>
              </AccordionItemContent>
            </AccordionItem>
          </AccordionRoot>
          <Box>
            <Text fontWeight="500" fontSize="sm" mb={2} color="gray.700">
              Add New Note
            </Text>
            <VStack spaceY={3}>
              {/* <CustomTextArea
                    inputProps={{
                      name: 'newNote',
                      spellCheck: true,
                      onChange: (e) => setCurrentNewNote(e.target.value),
                      value: currentNewNote,
                      onBlur: handleBlur,
                      height: '20',
                      placeholder: 'Type your consultation note here...',
                    }}
                    fieldProps={{
                      invalid: Boolean(errors.notes && touched.notes),
                      errorText: renderErrorText(errors.notes),
                    }}
                  /> */}

              <Box width={'100%'}>
                <TextEditorNew
                  //height="100px"
                  clearTrigger={clearCount} // Pass the counter
                  initialContent={currentNewNote}
                  saveContent={(e: any) => setCurrentNewNote(e)}
                />
              </Box>

              <HStack display={'none'} justify="flex-end" w="full">
                <IconButton
                  onClick={handleAddNote}
                  size="sm"
                  colorScheme="gray"
                  variant="solid"
                  disabled={currentNewNote.trim() === ''}
                  bg="primary.500"
                  color="white"
                  _hover={{ bg: 'gray.700' }}
                >
                  <FiPlus />
                </IconButton>
              </HStack>
            </VStack>
          </Box>
        </Card.Root>

        {/* Follow-up Section */}
        <Card.Root size="sm" mb={8} variant="elevated">
          <Card.Header>
            <HStack spaceX={2}>
              <Icon as={FiCalendar} color="gray.600" />
              <Text fontWeight="600" fontSize="lg" color="gray.800">
                Follow-up
              </Text>
            </HStack>
          </Card.Header>
          <Card.Body>
            <SimpleGrid gap={2} columns={{ base: 1, md: 3 }} width={'100%'}>
              <GridItem>
                <Box>
                  <Text fontWeight="500" fontSize="sm" mb={2} color="gray.700">
                    Follow-up Date
                  </Text>
                  <CustomDatePicker
                    onChange={(e) => {
                      setFieldValue('followup_date', e);
                    }}
                    defaultDate={values.followup_date}
                  />
                </Box>
              </GridItem>

              <GridItem>
                <CustomSelect
                  placeholder="Select type"
                  options={contactChannelsOptions}
                  onChange={(val) => {
                    setFieldValue('contact_by', val.value);
                  }}
                  label="Contact Type"
                  defaultValue={contactChannelsOptions.find(
                    (item) =>
                      item.value.toLowerCase() ===
                      values.contact_by.toLowerCase()
                  )}
                />
              </GridItem>

              <GridItem>
                <Button
                  bg="white"
                  border="1px solid"
                  mt={'25px'}
                  borderColor="primary.500"
                  rounded="md"
                  color="black"
                  width={'100%'}
                  _hover={{ bg: 'gray.50' }}
                  onClick={addFollowUp}
                  loading={AFloading}
                  size="md"
                >
                  Add Follow-up Task
                </Button>
              </GridItem>
            </SimpleGrid>
          </Card.Body>
        </Card.Root>

        {/* Action Buttons */}
        <HStack justify="space-between" spaceX={4} mt={8}>
          <Button
            onClick={onClose}
            variant="outline"
            minH="3rem"
            minW="8rem"
            colorScheme="gray"
          >
            Cancel
          </Button>
          <Button
            loading={saveClientLoading}
            colorScheme="gray"
            bg="primary.500"
            color="white"
            minH="3rem"
            minW="10rem"
            type="submit"
            _hover={{ bg: 'gray.900' }}
          >
            Save Consultation
          </Button>
        </HStack>
      </chakra.form>
    </Box>
  );
}
