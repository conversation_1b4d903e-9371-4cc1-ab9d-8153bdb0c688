import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getArrayParam, getNumberParam } from '@/utils/format-object';
import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createSupabaseServer();
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const org_id = searchParams.get('org_id') || '';
    const currentPage = getNumberParam(searchParams, 'page_number', 1);
    const size = getNumberParam(searchParams, 'items_per_page', 50);
    const non_active_client = searchParams.get('non_active_client') || false;
    const group_filter = getArrayParam(searchParams, 'groups');
    const filter_date = searchParams.get('filter_date') || '2025-04-09';
    const date_filter_status = searchParams.get('date_filter_status') || null;
    const date = new Date(filter_date).toISOString();

    const startIndex = (currentPage - 1) * size;
    const endIndex = startIndex + size - 1;

    const isNonActive = non_active_client !== 'true';

    let clientIds: any[] = [];
    // console.log('Params:', {
    //   id, // active_slp
    //   org_id,
    //   isNonActive,
    //   group_filter,
    //   filter_date,
    //   date_filter_status,
    //   clientIds, // If group_filter is applied
    // });

    // Build the base query
    let query = supabase
      .from(tableNames.clients)
      .select(
        `
      id, first_name, last_name, display_name, stage, middle_name, email, 
      active_slp, organization_id, slp_notes, province,
      packages(*),
      invoices(
        id, client_id, created_dt, session_type, total_hours, 
        total_price, memo, invoice_date
      ),
      bookings(
        *,
        services(*),
        slp_notes(
          *,
          invoice:invoices!slp_notes_invoice_id_fkey(
            *,
            services(*),
            purchased_package:transactions!invoices_purchased_package_id_fkey(
              *,
              package_offering(*),
              purchased_package_items(
                *,
                services!purchased_package_items_service_id_fkey(*)
              )
            )
          )
        )
      )
    `
      )
      .eq('active_slp', id)
      .eq('organization_id', org_id);

    if (filter_date && date_filter_status) {
      query = query.not(`${tableNames.invoices}.invoice_date`, 'is', null);

      if (date_filter_status === 'before') {
        query = query.lte(`${tableNames.invoices}.invoice_date`, date);
      } else if (date_filter_status === 'after') {
        query = query.gte(`${tableNames.invoices}.invoice_date`, date);
      }
    }
    //
    // Apply active/non-active filter
    // if (isNonActive) {
    //   query = query.eq('slp_notes', 'Active');
    // } else {
    //   query = query.neq('slp_notes', 'Active');
    // }
    if (isNonActive) {
      query = query.eq('slp_notes', 'Active');
    } else {
      query = query.or('slp_notes.neq.Active,slp_notes.is.null');
    }
    // Handle group filtering if needed
    if (group_filter?.length > 0) {
      const { data: taggedClients } = await supabase
        .from(tableNames.tags)
        .select('client_id')
        .eq('category', 'group')
        .eq('user_id', id);

      console.log('taggedClients', taggedClients);

      clientIds = taggedClients?.map((item) => item.client_id) || [];
      query = query.in('id', clientIds);
    }

    // Execute the main query with pagination
    const { data: clientsWithInvoices, error } = await query
      .order('id', { ascending: false })
      .range(startIndex, endIndex);

    if (error) throw new Error(error.message);

    // Get count data in a single query for statistics
    const { data: countData, count: totalClients } = await supabase
      .from(tableNames.clients)
      .select('slp_notes', { count: 'exact' })
      .eq('active_slp', id)
      .eq('organization_id', org_id);

    // Calculate counters for different client states
    const activeCount =
      countData?.filter((c) => c.slp_notes === 'Active').length || 0;
    const completedCount =
      countData?.filter((c) => c.slp_notes === 'Completed').length || 0;
    const onHoldCount =
      countData?.filter((c) => c.slp_notes === 'On Hold').length || 0;
    const doNotContactCount =
      countData?.filter((c) => c.slp_notes === 'Do Not Contact').length || 0;

    // Calculate filtered count for pagination
    let countQuery = supabase
      .from(tableNames.clients)
      .select('id', { count: 'exact', head: true })
      .eq('active_slp', id)
      .eq('organization_id', org_id);

    if (isNonActive) {
      countQuery = countQuery.eq('slp_notes', 'Active');
    } else {
      countQuery = countQuery.neq('slp_notes', 'Active');
    }

    // Apply the same group filter to count query if needed
    if (group_filter?.length > 0 && clientIds?.length) {
      countQuery = countQuery.in('id', clientIds);
    }

    const { count: filteredCount } = await countQuery;

    // Process clients and their invoices

    // console.log('query', query);

    const processedClients = clientsWithInvoices?.map((client: any) => {
      const invoices = client?.invoices || [];

      // console.log('Processing client:', client.id);
      // console.log('Invoices found:', invoices.length);

      if (!invoices?.length) {
        return {
          ...client,
          total_sessions: null,
          total_hours: null,
          total_spent: null,
          tx_count: null,
          ax_count: null,
          memo: null,
          last_session_date: null,
        };
      }

      // Calculate summary in a single pass
      const summary = invoices?.reduce(
        (acc: any, invoice: any) => {
          acc.total_sessions += 1;
          acc.total_hours += invoice.total_hours || 0;
          acc.total_spent += invoice.total_price || 0;
          if (invoice.session_type === 'Tx') acc.tx_count += 1;
          if (invoice.session_type === 'Ax') acc.ax_count += 1;

          if (
            !acc.latestDate ||
            new Date(invoice.invoice_date) > new Date(acc.latestDate)
          ) {
            acc.memo = invoice.memo ?? acc.memo;
            acc.latestDate = invoice.invoice_date;
          }

          return acc;
        },
        {
          total_sessions: 0,
          total_hours: 0,
          total_spent: 0,
          tx_count: 0,
          ax_count: 0,
          memo: null,
          latestDate: null,
        }
      );

      // console.log('summary', summary[0]);

      return {
        ...client,
        total_sessions: summary.total_sessions,
        total_hours: summary.total_hours,
        total_spent: summary.total_spent,
        tx_count: summary.tx_count,
        ax_count: summary.ax_count,
        memo: summary.memo,
        last_session_date: summary.latestDate,
      };
    });

    // console.log('processedClients---4', processedClients[0]);

    // // In your GET endpoint, before returning:
    // console.log('API Response:', {
    //   data: processedClients[0],
    // });
    // // In your API endpoint before returning:
    // console.log(
    //   'Full invoices data:',
    //   JSON.stringify(processedClients[0].invoices, null, 2)
    // );

    return NextResponse.json({
      data: processedClients,
      pagination: {
        current_page: currentPage,
        total_count: filteredCount,
        active_client_count: activeCount,
        total_client_count: totalClients,
        completedCount: completedCount,
        onHoldCount: onHoldCount,
        doNotContactCount: doNotContactCount,
        page_size: size,
      },
    });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
