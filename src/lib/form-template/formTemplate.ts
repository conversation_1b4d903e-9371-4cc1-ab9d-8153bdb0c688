import { getSlug<PERSON>romName, getSlugFromOrgName } from '@/utils/event';

export default function formTemplate(createdOrganization: any) {
  console.log('ccc---555', createdOrganization);
  const formPayload = [
    {
      description: 'To be filled out by the client',
      organization_id: createdOrganization?.id,
      organization_name: getSlugFromOrgName(createdOrganization?.name),
      organization_slug: createdOrganization?.slug,
      title: 'New Form',
      slug: getSlugFromName('new-form'),
      questions: [
        {
          icon: 'TfiText',
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          required: true,
          type: 'Textbox',
          heading: 'Short answer',
          default: 'true',
          page: 1,
          qt: ' Email',
        },
        {
          icon: 'TfiText',
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          qt: ' First Name',
          required: true,
          type: 'Textbox',
          heading: 'Short answer',
          page: 1,
          default: 'false',
        },
        {
          icon: 'TfiText',
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          qt: ' Last Name',
          required: true,
          heading: 'Short answer',
          default: 'false',
          page: 1,
          type: 'Textbox',
        },

        {
          icon: 'GoHash',
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          qt: 'Phone Number',
          required: false,
          heading: 'Number',
          default: 'false',
          page: 1,
          type: 'Textbox',
        },
      ],
    },
  ];

  return formPayload;
}
