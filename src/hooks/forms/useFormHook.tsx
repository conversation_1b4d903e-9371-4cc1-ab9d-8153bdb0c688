/* eslint-disable react-hooks/exhaustive-deps */

import { useGetFormByIdQuery } from '@/api/forms/get-form-by-id';
import NumberCom from '@/app/(dashboard)/admin/forms/_components/NumberCom';
import TextCom from '@/app/(dashboard)/admin/forms/_components/TextCom';
import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { getSlugFromName, getSlugFromOrgName } from '@/utils/event';
import { Text, useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import * as Yup from 'yup';

import { useGetAllFormsQuery } from '@/api/forms/get-all-forms';
import { queryKey } from '@/constants/query-key';

import { useGetAllAnswersQuery } from '@/api/answers/get-all-answers';
import { useGetUserByIdQuery } from '@/api/users/use-get-user-by-id';
import DateCom from '@/app/(dashboard)/admin/forms/_components/DateCom';
import RadioCom from '@/app/(dashboard)/admin/forms/_components/RadioCom';
import { IGetFormsFilterState } from '@/store/filters/forms';
import { useRecoilValue } from 'recoil';
import { generateUniqueId } from './util';

export const useFormHook = ({ slp, id }: { slp?: any; id?: any }) => {
  const [loading, setLoading] = useState(false);

  const [selectedClient, setSelectedClient] = useState<any>();
  const [selectedFormFromClient, setSelectedFormFromClient] = useState<any>();
  const [showSearchClient, setShowSearchClient] = useState(true);

  // mulitpages form

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const [allSelectedQuestions, setAllSelectedQuestions] = useState<any[]>([
    {
      icon: 'TfiText',
      id: generateUniqueId(),
      default: 'true',
      qt: 'Email',
      required: true,
      heading: 'Short answer',
      type: 'Textbox',
      title: '',
      description: '',
      page: 1, // Add page number to each question
    },
  ]);

  console.log('allSelectedQuestions in the hook', allSelectedQuestions);
  const getCurrentPageQuestions = () => {
    return allSelectedQuestions.filter((q) => q.page === currentPage);
  };

  // Add new page
  const addPage = () => {
    const newPageNumber = totalPages + 1;
    setTotalPages(newPageNumber);
    setCurrentPage(newPageNumber);
  };
  // Remove page (only if it's not the last page and not page 1)
  const removePage = (pageNumber: number) => {
    if (pageNumber === 1 || totalPages === 1) {
      // toaster.create({
      //   title: 'Cannot remove page',
      //   description: 'You cannot remove the first page or the only page.',
      //   status: 'error',
      // });
      return;
    }

    // Remove all questions from this page
    setAllSelectedQuestions((prev) =>
      prev.filter((q) => q.page !== pageNumber)
    );

    // Update page numbers for pages after the removed page
    setAllSelectedQuestions((prev) =>
      prev.map((q) => (q.page > pageNumber ? { ...q, page: q.page - 1 } : q))
    );

    // Update total pages
    setTotalPages((prev) => prev - 1);

    // Adjust current page if necessary
    if (currentPage >= pageNumber) {
      setCurrentPage((prev) => Math.max(1, prev - 1));
    }
  };

  const switchToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // Modified setAllSelectedQuestions to handle page assignment
  const addQuestionToCurrentPage = (questionData: any) => {
    console.log('questionData', questionData);
    const questionWithPage = {
      ...questionData,
      page: currentPage,
    };

    setAllSelectedQuestions((prev) => {
      const isEdit = prev.some((q) => q.id === questionData.id);
      if (isEdit) {
        return prev.map((q) =>
          q.id === questionData.id ? questionWithPage : q
        );
      } else {
        return [...prev, questionWithPage];
      }
    });
  };

  // Get questions for a specific page
  const getQuestionsForPage = (pageNumber: number) => {
    return allSelectedQuestions.filter((q) => q.page === pageNumber);
  };

  // Move question to different page
  const moveQuestionToPage = (questionId: string, targetPage: number) => {
    if (targetPage >= 1 && targetPage <= totalPages) {
      setAllSelectedQuestions((prev) =>
        prev.map((q) => (q.id === questionId ? { ...q, page: targetPage } : q))
      );
    }
  };
  const getPageSummary = () => {
    const summary = [];
    for (let i = 1; i <= totalPages; i++) {
      const pageQuestions = getQuestionsForPage(i);
      summary.push({
        page: i,
        questionCount: pageQuestions.length,
        hasRequired: pageQuestions.some((q) => q.required),
      });
    }
    return summary;
  };

  // Validate if all pages have at least one question
  const validatePages = () => {
    for (let i = 1; i <= totalPages; i++) {
      const pageQuestions = getQuestionsForPage(i);
      if (pageQuestions.length === 0) {
        return {
          isValid: false,
          message: `Page ${i} has no questions. Please add at least one question to each page.`,
        };
      }
    }
    return { isValid: true, message: '' };
  };

  //multipages form end
  const router = useRouter();
  const queryClient = useQueryClient();
  const filter = useRecoilValue(IGetFormsFilterState);

  const [formIdToBeDel, setFormIdToBeDel] = useState('');

  // Filter questions by current page

  console.log('allSelectedQuestions', allSelectedQuestions);

  const [selectedQuestionType, setSelectedQuestionType] = useState<
    string | null
  >(null);
  const path = usePathname();

  const searchParams = useSearchParams();
  const organizationId = searchParams.get('organization_id');
  const slp_id = path.split('/')[2];

  console.log('slp_id', slp_id);

  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;

  const org = dataOrg?.UserState?.organization;

  console.log('dataOrg', dataOrg);

  const {
    data: UsersData,
    // isFetching,
    // isLoading: dIsLoading,
    // refetch,
  } = useGetUserByIdQuery(Number(slp_id), {
    enabled: Boolean(slp_id),
  });
  console.log('org', org);

  // queries

  const {
    data: AllAnswers,
    isLoading: AllAnswersLoading,
    refetch: refetchAnswers,
  } = useGetAllAnswersQuery({
    organizationId: organizationId ? Number(organizationId) : org?.id,
  });

  const {
    data: GetAllFormsData,
    isLoading: GetAllFormsLoading,
    refetch: refetchForms,
  } = useGetAllFormsQuery({
    organizationId: organizationId ? Number(organizationId) : org?.id,
  });
  const { data: FormData, isLoading: FormDataIsloading } = useGetFormByIdQuery(
    Number(id),
    { enabled: Boolean(id) }
  );

  // console.log('user from server', slp);

  // console.log('allSekected', allSelectedQuestions);

  // disclosures

  const { onClose, onOpen, open } = useDisclosure();
  const {
    onClose: onCloseDelete,
    onOpen: onOpenDelete,
    open: openDelete,
  } = useDisclosure();
  const {
    onClose: onCloseLearn,
    onOpen: onOpenLearn,
    open: openLearn,
  } = useDisclosure();

  // FUNCTIONS

  const handleGetFormFromLoopedForms = (form: any) => {
    setSelectedFormFromClient(form);
  };

  const handleAddClient = (props: any) => {
    console.log('props', props);
    setSelectedClient(props);
    setShowSearchClient(false);
  };

  // console.log('allselectedq', allSelectedQuestions);

  const handleOpenLearnModal = () => onOpenLearn();

  const handleQuestionClick = (q: any) => {
    setSelectedQuestionType(q);
    onOpen();
  };
  // function to remove selected question from allSelectedQuestions
  const removeSelectedQuestion = (id: any) => {
    setAllSelectedQuestions((prev) => prev.filter((q) => q.id !== id));
  };
  const handleConfirmDeleteFormModal = (id: any) => {
    setFormIdToBeDel(id);
    onOpenDelete();
  };

  const handleDeleteForm = async (id: any) => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from(tableNames.forms)
        .delete()
        .eq('id', Number(id));
      if (error) throw error;

      refetchForms();

      await queryClient.refetchQueries({
        queryKey: [queryKey.forms.getAllForms, filter],
      });
      toaster.create({
        description: 'Form Deleted Successfully',
        type: 'success',
      });
      onCloseDelete();
    } catch (error) {
      toaster.create({
        description: 'Something went wrong.',
        type: 'error',
      });
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const QuestionModalContent = ({ q }: { q: any }) => {
    switch (q.type) {
      case 'Textbox':
        return (
          <TextCom
            onClose={onClose}
            generateUniqueId={generateUniqueId}
            q={q}
            addQuestionToCurrentPage={addQuestionToCurrentPage}
            // setAllSelectedQuestions={setAllSelectedQuestions}
          />
        );
      case 'Number':
        return (
          <NumberCom
            onClose={onClose}
            q={q}
            generateUniqueId={generateUniqueId}
            addQuestionToCurrentPage={addQuestionToCurrentPage}
            // setAllSelectedQuestions={setAllSelectedQuestions}
          />
        );
      case 'Date':
        return (
          <DateCom
            onClose={onClose}
            q={q}
            addQuestionToCurrentPage={addQuestionToCurrentPage}
            // setAllSelectedQuestions={setAllSelectedQuestions}
            generateUniqueId={generateUniqueId}
          />
        );
      case 'Single choice':
      case 'Multiple choice':
        return (
          <RadioCom
            onClose={onClose}
            q={q}
            addQuestionToCurrentPage={addQuestionToCurrentPage}
            // setAllSelectedQuestions={setAllSelectedQuestions}
            generateUniqueId={generateUniqueId}
          />
        );
      default:
        return <Text>Select a question type.</Text>;
    }
  };
  // =========== Derived Data & Memos ===========

  const initialValues = {
    title: '',
    description: '',
    slug: '',
    organization_id: slp?.organization_id || slp?.organization?.id || '',
    organization_name: slp?.organization?.name || '',
  };

  const FormSchema = Yup.object().shape({
    title: Yup.string()
      .required('Title is required')
      .max(100, 'Title must be 100 characters or less'),
    description: Yup.string()
      .required('Description is required')
      .min(1, 'Description must be more than 1 character')
      .max(500, 'Description must be 500 characters or less'),
    slug: Yup.string()
      .required('URL path is required')
      .matches(
        /^[a-z0-9-]*$/,
        'URL path can only contain lowercase letters, numbers, and hyphens'
      )
      .test(
        'no-trailing-slash',
        'URL path cannot end with a hyphen',
        (value) => !value.endsWith('-')
      ),
  });

  const handleSubmitNewForm = () => {
    handleSubmit();
  };
  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    setValues,
    setErrors,
  } = useFormik({
    initialValues: initialValues,
    validationSchema: FormSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);

        if (allSelectedQuestions?.length === 0) {
          toaster.create({
            description: 'Please add at least one question.',
            type: 'error',
          });
          setLoading(false);
          return;
        }

        // Helper function to generate unique title (case-insensitive and whitespace-normalized)
        const generateUniqueTitle = async (
          baseTitle: string,
          organizationId: number
        ): Promise<string> => {
          let title = baseTitle;
          let counter = 0;
          let isUnique = false;

          // Helper function to normalize title for comparison
          const normalizeTitle = (str: string): string => {
            return str.toLowerCase().trim().replace(/\s+/g, ' ');
          };

          while (!isUnique) {
            // Get all forms for this organization
            const { data: existingForms, error: checkError } = await supabase
              .from(tableNames.forms)
              .select('id, title')
              .eq('organization_id', organizationId);

            if (checkError) throw checkError;

            // Check if any existing form has a similar title (case-insensitive and whitespace-normalized)
            const normalizedCurrentTitle = normalizeTitle(title);
            const hasConflict = existingForms.some((form) => {
              const normalizedExistingTitle = normalizeTitle(form.title);
              return (
                normalizedExistingTitle === normalizedCurrentTitle &&
                (!id || form.id !== Number(id))
              );
            });

            // If no conflict found, this title is unique
            if (!hasConflict) {
              isUnique = true;
              return title;
            }

            // Title conflicts with existing one, increment counter and try again
            counter++;
            title = `${baseTitle} (${counter})`;
          }

          return title; // This line should never be reached, but TypeScript requires it
        };

        // Get the organization ID for the title check
        const orgId =
          Number(organizationId) ||
          slp?.organization_id ||
          values?.organization_id;

        // Generate unique title
        const uniqueTitle = await generateUniqueTitle(values.title, orgId);

        const insert = {
          title: uniqueTitle, // Use the unique title
          description: values.description,
          logo_url: organizationId
            ? UsersData?.organization?.logo_url
            : org?.logo_url,
          user_id: UsersData?.id || dataOrg?.UserState?.id,
          slug: values.slug || getSlugFromName(uniqueTitle), // Use unique title for slug too
          organization_name: slp
            ? getSlugFromOrgName(slp?.organization?.name)
            : values?.organization_name,
          organization_id: orgId,
          organization_slug: UsersData?.organization?.slug || org?.slug,
          questions: allSelectedQuestions,
        };

        console.log('insert', insert);
        // console.log(
        //   'organization_name',
        //   slp
        //     ? getSlugFromOrgName(slp?.organization?.name)
        //     : values?.organization_name
        // );
        // console.log('values', values);
        // console.log('vi---1', getSlugFromOrgName(slp?.organization?.name));
        // console.log('vi---1', getSlugFromOrgName(values?.organization_name));

        if (!id || !FormData) {
          // console.log('insert-----333', insert);

          const { error } = await supabase
            .from(tableNames.forms)
            .insert(insert);
          if (error) throw error;

          toaster.create({
            description: 'Form Created Successfully',
            type: 'success',
          });
        } else {
          // Identify updated questions
          const updatedQuestions = allSelectedQuestions.filter(
            (newQ: any) =>
              !FormData.questions.some(
                (oldQ: any) =>
                  oldQ.id === newQ.id &&
                  JSON.stringify(oldQ) === JSON.stringify(newQ)
              )
          );

          // Identify deleted questions
          const deletedQuestionIds = FormData.questions
            .filter(
              (q: any) =>
                !allSelectedQuestions.some((selected) => selected.id === q.id)
            )
            .map((q: any) => q.id);

          // Update the form in the database
          const { error } = await supabase
            .from(tableNames.forms)
            .update(insert)
            .eq('id', Number(FormData?.id));
          if (error) throw error;

          if (updatedQuestions.length > 0 || deletedQuestionIds.length > 0) {
            // Fetch all form_answers for this form
            const { data: formAnswers, error: fetchError } = await supabase
              .from(tableNames.form_answers)
              .select('id, answer_details')
              .eq('form_id', Number(FormData?.id));

            if (fetchError) throw fetchError;

            if (formAnswers?.length > 0) {
              for (const row of formAnswers) {
                // Extract existing answers
                const existingAnswers = row.answer_details || [];

                // Update edited questions
                const updatedAnswers = existingAnswers.map((ans: any) =>
                  updatedQuestions.some((q) => q.id === ans.id)
                    ? {
                        ...ans,
                        ...updatedQuestions.find((q) => q.id === ans.id),
                      }
                    : ans
                );

                // Set `show_answers` to false for deleted questions
                const finalAnswers = updatedAnswers.map((ans: any) =>
                  deletedQuestionIds.includes(ans.id)
                    ? { ...ans, show_answers: 'false' }
                    : ans
                );

                // Update the database
                const { error: updateError } = await supabase
                  .from(tableNames.form_answers)
                  .update({ answer_details: finalAnswers }) // Update JSONB array
                  .eq('id', row.id); // Update specific row

                if (updateError) throw updateError;
              }
            }
          }

          toaster.create({
            description: 'Form Updated Successfully',
            type: 'success',
          });
        }
        console.log('-------refetching queries');

        refetchForms();
        refetchAnswers();

        console.log('-------validate');

        await queryClient.refetchQueries({
          queryKey: [queryKey.forms.getAllForms, filter],
        });

        await queryClient.invalidateQueries({
          queryKey: [queryKey.forms.getFormById, Number(FormData?.id)],
        });
        await queryClient.refetchQueries({
          queryKey: [queryKey.forms.getFormAnswersById, Number(FormData?.id)],
        });
      } catch (error) {
        setLoading(false);
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
        console.error(error);
      } finally {
        setLoading(false);

        if (organizationId && slp_id) {
          const url = `/slp/${slp_id}?view=dashboard&organization_id=${organizationId}&tab=forms`;
          router.replace(url);
        } else {
          router.replace('/admin/forms?view=dashboard');
        }
      }
    },
  });

  //   EFFECTS

  useEffect(() => {
    if (FormData || selectedFormFromClient) {
      const formToLoad = FormData || selectedFormFromClient;
      setValues({
        title: formToLoad.title || '',
        description: formToLoad.description || '',
        slug: formToLoad.slug || '',
        organization_id: Number(organizationId) || formToLoad.organization_id,
        organization_name: formToLoad.organization_name,
      });

      setAllSelectedQuestions(formToLoad.questions || []);

      // Calculate total pages based on loaded questions
      if (formToLoad.questions?.length > 0) {
        const maxPage = Math.max(
          ...formToLoad.questions.map((q: any) => q.page || 1)
        );
        setTotalPages(maxPage);
      }
    }
  }, [FormData, id, selectedFormFromClient]);

  return {
    loading,
    values,
    errors,
    touched,
    handleChange,
    GetAllFormsData,
    handleQuestionClick,
    setErrors,
    selectedQuestionType,
    setFieldValue,
    AllAnswers: AllAnswers?.data?.allAnswers,
    AllAnswersLoading,
    onClose,
    handleDeleteForm,
    setAllSelectedQuestions,
    allSelectedQuestions,
    onOpen,
    QuestionModalContent,
    generateUniqueId,
    handleOpenLearnModal,
    handleAddClient,
    // Multi-page functionality
    currentPage,
    totalPages,
    getCurrentPageQuestions,
    addPage,
    removePage,
    switchToPage,
    addQuestionToCurrentPage,
    getQuestionsForPage,
    moveQuestionToPage,
    getPageSummary,
    validatePages,

    // Helper functions
    setCurrentPage,
    setTotalPages,
    formIdToBeDel,
    handleGetFormFromLoopedForms,
    GetAllFormsLoading,
    handleConfirmDeleteFormModal,
    openDelete,
    onCloseDelete,
    removeSelectedQuestion,
    setValues,
    organizationSlug: UsersData?.organization?.slug || org?.slug,
    onOpenDelete,
    organizationName: slp?.organization?.name || '',
    FormDataIsloading,
    onCloseLearn,
    onOpenLearn,
    openLearn,
    showSearchClient,
    org,
    selectedClient,
    setShowSearchClient,
    open,
    handleSubmitNewForm,
    handleSubmit,
  };
};
