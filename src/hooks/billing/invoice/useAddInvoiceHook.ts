import { createInvoice } from '@/api/invoices';

import { ToastMessages } from '@/constants/toast-messages';
// import { useAdminSlps } from '@/app/(dashboard)/admin/slps/all-slps/useAdminSlps';
import { useFormik } from 'formik';
import * as Yup from 'yup';

import { useGetMaxInvoiceNumberQuery } from '@/api/invoices/max-invoice-number';
import { useGetAllSlpQuery } from '@/api/users/get-slps';
import { toaster } from '@/components/ui/toaster';
import moment from 'moment';
import { useState } from 'react';

export const useAddInvoiceHook = ({
  refetch,
  onClose,
  refetchClient,
  initialValues: _initial = {},
}: {
  refetch: any;
  onClose: any;
  refetchClient?: any;
  initialValues?: any;
}) => {
  const raw =
    typeof window !== 'undefined' ? localStorage.getItem('UserState') : null;
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const searchParams = new URLSearchParams(window.location.search);
  const orgIdFromUrl = searchParams.get('organization_id');

  const { data: AllSlp } = useGetAllSlpQuery({
    organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
  });

  const { data: MaxInvoiceNumber } = useGetMaxInvoiceNumberQuery(
    Number(org?.id),
    { enabled: Boolean(org?.id) }
  );

  const [newClient, setNewClient] = useState(false);
  const [isExistingClient, setIsExistingClient] = useState('no');
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<Array<any>>([]);

  const SlpOptions = AllSlp?.map((item: any) => ({
    label: item?.name || `${item?.first_name} ${item?.last_name}`,
    value: item.id || item?.user_id,
    organization_id: item?.organization_id,
  }));

  const clientOptions = [
    { label: 'Yes', value: 'yes' },
    { label: 'No', value: 'no' },
  ];

  const SessionOptions = [
    { label: 'Tx', value: 'Tx' },
    { label: 'Ax', value: 'Ax' },
    { label: 'Workshop', value: 'Workshop' },
  ];

  const sessionDuration = [
    {
      label: '30',
      value: '30',
    },
    {
      label: '45',
      value: '45',
    },
    {
      label: '60',
      value: '60',
    },
    {
      label: '90',
      value: '90',
    },
    {
      label: '120',
      value: '120',
    },
  ];

  const initialValues = {
    client_id: '',
    product: '',
    status: 'Active',
    invoice_number: '',
    client: '',
    qty: '',
    email: '',
    invoice_date: moment().format('YYYY-MM-DDTHH:mm'),
    name: '',
    slp_id: '',
    slp: '',
    session_type: 'Tx',
    total_hours: '',
    total_price: '',
    memo: '',
    organization_id: '',
    // linked_client_id: '',
    ..._initial,
  };

  //   form submission

  // validate invoice
  const validationSchema = Yup.object({
    invoice_number: Yup.number()
      .typeError('Invoice number must be a number')
      .moreThan(0, 'Invoice number cannot be negative'),
    session_type: Yup.string().trim().required('Session type is required'),
    slp: Yup.string().trim().required('SLP type is required'),
    total_hours: Yup.number().typeError('Total Minutes must be a number'),
    qty: Yup.number()
      .typeError('Quantity must be  a number.')
      .moreThan(0, 'Qty cannot be negative'),
    total_price: Yup.number()
      .typeError('Price must be a number.')
      .moreThan(0, 'Price cannot be negative'),
  });

  const {
    values,
    handleSubmit,
    errors,
    handleChange,
    touched,
    resetForm,
    handleBlur,
    setFieldValue,
  } = useFormik({
    initialValues: initialValues,
    validationSchema,
    onSubmit: async (values: any) => {
      try {
        setLoading(true);

        if (!values?.client_id) {
          throw new Error('Client is required');
        }

        const payload = {
          client_id: values?.client_id,
          // linked_client_id: values?.linked_client_id ?? null,
          status: 'Active',
          name: `${values?.first_name} ${values?.last_name} `,
          slp: values?.slp,
          qty: values?.qty,
          total_price: values?.total_price,
          product: values?.product,
          invoice_number: Number(MaxInvoiceNumber) + 1,
          memo: values?.memo,
          slp_id: values?.slp_id,
          total_hours: values?.total_hours,
          session_type: values?.session_type,
          invoice_date: values?.invoice_date,
          invoice_date_raw: moment(values?.invoice_date).format('YYYY-MM-DD'),
          organization_id: values?.organization_id,
        };

        const res = await createInvoice(payload);

        if (res) {
          toaster.create({
            description: ToastMessages.operationSuccess,
            type: 'success',
          });
          onClose();
          await refetchClient?.();
          await refetch?.();
          resetForm();
        }
      } catch (error: any) {
        toaster.create({
          description: error.message || ToastMessages.somethingWrong,
          type: 'error',
        });
        setLoading(false);
      } finally {
        setLoading(false);
      }
    },
  });

  const handleFormSubmit = (e: any) => {
    e.preventDefault();
    handleSubmit();
  };

  return {
    values,
    handleFormSubmit,
    errors,
    handleChange,
    touched,
    handleBlur,
    setFieldValue,
    newClient,
    SlpOptions,
    sessionDuration,
    SessionOptions,
    setNewClient,
    isExistingClient,
    setIsExistingClient,
    resetForm,
    loading,
    clientOptions,
    searchResult,
    setSearchResult,
  };
};
