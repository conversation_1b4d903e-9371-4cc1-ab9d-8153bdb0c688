/* eslint-disable react-hooks/exhaustive-deps */

import {
  checkDuplicateBooking,
  createClient,
  createClientEmails,
  findExistingClient,
} from '@/api/bookings';
import { useCreateBookingMutation } from '@/api/bookings/create-booking';
import { useGetAllClientsQuery } from '@/api/clients/get-all-clients';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { useGetAllSlpQuery } from '@/api/users/get-slps';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { tableNames } from '@/constants/table_names';
import { ToastMessages } from '@/constants/toast-messages';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import supabase from '@/lib/supabase/client';
import { AllClientsFilterState } from '@/store/filters/clients';
import { useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';
import moment from 'moment';
import { usePathname } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useRecoilState } from 'recoil';
import * as Yup from 'yup';

interface BookingHookProps {
  bookingOnClose: () => void;
  refetch?: () => void;
  data?: any; // Specific client data passed as props
}

export const useAddBookingHook = ({
  bookingOnClose,
  refetch,
  data,
}: BookingHookProps) => {
  const [filter] = useRecoilState(AllClientsFilterState);
  const [newClient, setNewClient] = useState(false);
  const [isExistingClient, setIsExistingClient] = useState('yes');
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<Array<any>>([]);
  const [showSearch, setShowSearch] = useState(true);
  const { refetch: refetchAllClient } = useGetAllClientsQuery(filter);
  const queryClient = useQueryClient();

  const path = usePathname();
  const slp_id = path.split('/')[2];

  const { UserFromQuery } = useSupabaseSession();
  const { mutateAsync: CreateBooking, isLoading: CreateBookingLoading } =
    useCreateBookingMutation();
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const searchParams = new URLSearchParams(window.location.search);
  const orgIdFromUrl = searchParams.get('organization_id');

  const { data: slpData, isLoading: slpLoading } = useGetAllSlpQuery({
    organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
  });

  const clientOptions = [
    { label: 'Yes', value: 'yes' },
    { label: 'No', value: 'no' },
  ];

  const handleSelectClient = (item: any) => {
    setFieldValue('first_name', item.first_name);
    setFieldValue('last_name', item.last_name);
    setFieldValue(
      'display_name',
      item.display_name || `${item.first_name} ${item.last_name}` || ''
    );
    setFieldValue('email', item.email || item.email);
    setFieldValue('client_id', item.id);
    setFieldValue('province', item.province);
    setFieldValue('phone', item.phone);
    setShowSearch(false);
  };

  const SlpOptions = useMemo(() => {
    return slpData
      ? slpData
          .filter((user: any) => user.status === 'Active')
          .map((item: any) => ({
            label: `${item?.first_name} ${item?.last_name}`,
            value: item?.email,
            id: item?.id,
          }))
      : [];
  }, [slpData]);

  const { data: ServicesData } = useGetServicesQuery(
    dataOrg?.UserState?.organization_id || UserFromQuery?.organization_id,
    {
      enabled:
        Boolean(dataOrg?.UserState?.organization_id) ||
        Boolean(UserFromQuery?.organization_id),
    }
  );

  const servicesOption = useMemo(
    () =>
      ServicesData?.services?.map((item: any) => ({
        label: item?.name,
        value: item,
      })) || [],
    [ServicesData]
  );

  const baseSchema = Yup.object({
    appointment: Yup.string().required(),
    event: Yup.string().required('Select Event'),
    assigned_to: Yup.string().required('Select Therapist'),
  });

  const getValidationSchema = (isExistingClient: string) =>
    baseSchema.shape({
      province:
        isExistingClient === 'yes' || !showSearch
          ? Yup.string().nullable()
          : Yup.string().nullable(),
      client_id:
        isExistingClient === 'yes'
          ? Yup.string().required('Client ID is required')
          : Yup.string().nullable(),
      first_name:
        isExistingClient === 'yes'
          ? Yup.string().nullable()
          : Yup.string().required('Enter First name'),
      last_name:
        isExistingClient === 'yes'
          ? Yup.string().nullable()
          : Yup.string().required('Enter Last name'),
      email:
        isExistingClient === 'yes'
          ? Yup.string().nullable()
          : Yup.string().email().required('Enter a valid email'),
      phone:
        isExistingClient === 'yes' || !showSearch
          ? Yup.string().nullable()
          : Yup.string().matches(
              /^\+?[0-9 -]+$/,
              'Invalid phone number format'
            ),
    });

  const validationSchema = useMemo(
    () => getValidationSchema(isExistingClient),
    [isExistingClient, showSearch]
  );

  // Separate function to handle existing client booking
  const handleExistingClientBooking = async (values: any) => {
    if (!values.client_id) {
      throw new Error('Please select a client');
    }
    return values.client_id;
  };

  // Separate function to handle new client creation
  const handleNewClientBooking = async (values: any) => {
    // Check if client already exists
    const existingClient = await findExistingClient(values.email);

    if (existingClient?.length > 0) {
      throw new Error(
        'A client with this email already exists. Please use the existing client option.'
      );
    }

    // Create new client
    const newClientData = {
      email: values.email.toLowerCase(),
      first_name: values.first_name,
      last_name: values.last_name,
      display_name: `${values?.first_name} ${values?.last_name}`,
      phone: values.phone,
      province: values.province,
      lead_created: values.appointment,
      active_slp: SlpOptions?.length === 1 ? SlpOptions[0]?.id : slp_id,
      last_consultation_date: values.appointment,
      organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
    };

    const newClient = await createClient(newClientData);
    if (!newClient || !newClient[0]) {
      throw new Error('Failed to create new client');
    }

    const clientId = newClient[0].id;
    setNewClient(true);

    // Create client emails
    await createClientEmails(
      clientId,
      newClientData.email.toLowerCase(),
      newClient[0]?.organization_id
    );

    // Log client creation in client_activities
    await supabase.from(tableNames.client_activities).insert({
      client_id: clientId,
      activity_type: 'client_created',
      organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
      activity_date: new Date().toISOString(),
      details: {
        created_by: 'booking',
      },
    });

    return clientId;
  };

  const {
    values,
    handleSubmit,
    errors,
    handleChange,
    submitCount,
    touched,
    setValues,
    resetForm,
    handleBlur,
    setFieldValue,
  } = useFormik({
    initialValues: {},
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values: any) => {
      console.log('values', values);
      try {
        setLoading(true);

        let clientId: string;

        // Handle client creation/selection based on type
        if (isExistingClient === 'yes') {
          clientId = await handleExistingClientBooking(values);
        } else {
          clientId = await handleNewClientBooking(values);
        }

        // Check for duplicate bookings
        const duplicate = await checkDuplicateBooking(
          {
            ...values,
            client_id: clientId,
          },
          orgIdFromUrl ? Number(orgIdFromUrl) : org?.id
        );

        if (duplicate.data && duplicate.data.length > 0) {
          throw new Error(
            'A booking already exists for this client at this time'
          );
        }

        // Get SLP ID
        const slpId =
          SlpOptions?.length === 1
            ? SlpOptions?.[0]?.id
            : SlpOptions?.find((item: any) => item.value === values.assigned_to)
                ?.id;

        if (!slpId) {
          throw new Error('Invalid therapist selection');
        }

        // Create booking payload
        const payload = {
          ...values,
          client_id: clientId,
          appointment: moment(values.appointment)
            .utc()
            .format('YYYY-MM-DD HH:mm:ss+00'),
          booking_created_at_raw: moment()
            .utc()
            .format('YYYY-MM-DD HH:mm:ss+00'),
          slp_id: slpId,
          calendly_event_type: 'invitee.created',
          organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
        };

        // Clean up payload
        delete payload.product_id;
        delete payload.display_name;

        console.log('payload', payload);

        // Insert booking
        const bookingData = await CreateBooking({
          payload,
          event: {
            data: {
              type: 'event',
              attributes: {
                profile: {
                  data: {
                    type: 'profile',
                    attributes: {
                      email: values?.email,
                      first_name: values?.first_name,
                      last_name: values?.last_name,
                    },
                    // id: String(values?.client_id),
                  },
                },

                metric: {
                  data: {
                    type: 'metric',
                    attributes: {
                      name: 'Booking Created',
                    },
                  },
                },
                properties: {
                  booking_date: new Date(values?.appointment).toISOString(),
                  client_province: values?.province,
                  booking_name: values?.event,
                },
              },
            },
          },
          // event: {
          //   data: {
          //     type: 'event',
          //     attributes: {
          //       data: {
          //         type: 'profile',
          //         attributes: {
          //           email: values?.email,
          //           first_name: values?.first_name,
          //           last_name: values?.last_name,
          //           id: values?.client_id,
          //         },
          //       },
          //       metric: {
          //         data: {
          //           type: 'metric',
          //           attributes: {
          //             name: 'Booking Created',
          //           },
          //         },
          //       },
          //       properties: {
          //         bookingDate: values.appointment,
          //         clientProvince: values?.province,
          //         bookingName: values?.event,
          //       },
          //     },
          //   },
          // },
          organization_id: UserFromQuery?.organization_id,
        });

        // Log booking activity
        await supabase.from(tableNames.client_activities).insert({
          client_id: clientId,
          activity_type: 'booking_created',
          organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
          activity_date: new Date().toISOString(),
          details: {
            ...payload,
            booking_id: bookingData?.id,
            status: 'Pending',
          },
        });

        // Invalidate queries
        await queryClient.invalidateQueries({
          queryKey: [queryKey.client.getById, Number(clientId)],
        });
        await queryClient.invalidateQueries({
          queryKey: [
            queryKey.bookings.getSlpBookings,
            {
              date: moment(values?.appointment).format('YYYY-MM-DD'),
              slpId: Number(slpId),
            },
          ],
        });

        refetch?.();
        refetchAllClient();
        toaster.create({
          description: ToastMessages.operationSuccess,
          type: 'success',
        });

        resetForm();
      } catch (error: any) {
        console.error('Booking creation error:', error);
        toaster.create({
          description: error.message || ToastMessages.somethingWrong,
          type: 'error',
        });
      } finally {
        setLoading(false);
        bookingOnClose();
      }
    },
  });

  useEffect(() => {
    // Set default service when services are loaded and no service is selected
    if (servicesOption.length > 0 && !values.service_id) {
      setFieldValue('service_id', servicesOption[0].value.id);
      setFieldValue('event', servicesOption[0].value.name);
    }
  }, [servicesOption, values.service_id, setFieldValue]);

  const handleFormSubmit = async (e: any) => {
    e.preventDefault();
    // console.log('form is ', values);
    // console.log('form is ', values);
    // console.log('client  is ', data);
    // console.log('UserFromQuery  is ', UserFromQuery);
    // const klaviyoApi = new KlaviyoAPI(
    //   UserFromQuery?.organization?.klaviyo_details?.access_token,
    //   UserFromQuery?.organization?.klaviyo_details?.refresh_token
    // );

    // try {
    //   console.log('trying to create event');

    //   await klaviyoApi.trackEvent(
    //     UserFromQuery?.organization_id,
    //     KlaviyoActions.BOOKING_CREATED,
    //     {
    //       data: {
    //         type: 'event',
    //         attributes: {
    //           data: {
    //             type: 'profile',
    //             attributes: {
    //               email: values?.email,
    //               first_name: values?.first_name,
    //               last_name: values?.last_name,
    //               id: values?.client_id,
    //             },
    //           },
    //           metric: {
    //             data: {
    //               type: 'metric',
    //               attributes: {
    //                 name: 'Booking Created',
    //               },
    //             },
    //           },
    //           properties: {
    //             bookingDate: values.appointment,
    //             clientProvince: values?.province,
    //             bookingName: values?.event,
    //           },
    //         },
    //       },
    //     },
    //     supabase
    //   );
    // } catch (error) {
    //   console.error('error is ', error);
    // }

    // return;
    handleSubmit();
  };

  useEffect(() => {
    if (data && isExistingClient !== 'yes') {
      setIsExistingClient('yes');
    }

    if (isExistingClient && !data) {
      setShowSearch(true);
    }
    setValues({
      first_name: data?.first_name || '',
      last_name: data?.last_name || '',
      email: data?.email || data?.email || '',
      display_name: data?.display_name
        ? `${data?.first_name} ${data?.last_name}`
        : '',
      phone: data?.phone || '',
      province: data?.province || '',
      client_id: data?.id || '',
      appointment: data?.appointment || moment().format('YYYY-MM-DDTHH:mm'),
      service_id: '',
      event: '',
      assigned_to: values?.assigned_to || '',
    });
  }, [data, isExistingClient]);

  useEffect(() => {
    if (SlpOptions.length === 1) {
      setFieldValue('assigned_to', SlpOptions[0].value);
    }
  }, [SlpOptions, setFieldValue]);

  return {
    values,
    handleFormSubmit,
    errors,
    handleChange,
    touched,
    submitCount,
    handleBlur,
    SlpOptions,
    slpLoading,
    setFieldValue,
    newClient,
    setNewClient,
    handleSelectClient,
    servicesOption,
    isExistingClient,
    showSearch,
    setIsExistingClient,
    resetForm,
    loading,
    clientOptions,
    searchResult,
    setSearchResult,
    setShowSearch,
    org,
    CreateBookingLoading,
  };
};
