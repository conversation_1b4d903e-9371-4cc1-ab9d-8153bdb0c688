import { useDisclosure } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { toaster } from '@/components/ui/toaster';
import { ToastMessages } from '@/constants/toast-messages';
import { useRouter } from 'next/navigation';
import { createSupabaseClient } from '@/lib/supabase/client';
import { env } from '@/constants/env';
import { useUpdateGmailAccessApi } from '@/api/integrations/update-google-access';
import { useGetUserByEmailQuery } from '@/api/users/get-user-by-email';
// import { refreshAccessToken } from '@/app/api/(public)/public/calendar/route';
// import { gmail } from 'googleapis/build/src/apis/gmail';

export const useIntegrations = (user: any) => {
  const router = useRouter();
  const supabase = createSupabaseClient();
  const [stripeLoading, setStripeLoading] = useState(false);
  const [disconnectLoading, setDisconnectLoading] = useState(false);
  const [calendlyLoading, setCalendlyLoading] = useState(false);
  const [googleService, setGoogleService] = useState<'gmail' | 'calendar'>();
  const { mutateAsync, isLoading } = useUpdateGmailAccessApi();
  const { data: userData, refetch } = useGetUserByEmailQuery(user?.email, {
    enabled: Boolean(user?.email),
  });

  // const [stripeCanDisconnect, setStripeCanDisconnect] = useState(false);
  const stripeDisconnectDisclosure = useDisclosure();
  const calendlyDisconnectDisclosure = useDisclosure();
  const gmailDisconnectDisclosure = useDisclosure();
  const klaviyoDisconnectDisclosure = useDisclosure();
  const editKlaviyoDisclosure = useDisclosure();

  const handleIntegrationState = (query: URLSearchParams) => {
    const successMessage = query.get('success');
    const errorMessage = query.get('error');
    console.log(successMessage);
    // console.log(errorMessage);

    if (successMessage) {
      toaster.create({
        // title: 'Integration Successful',
        description: `successMessage.replace(/_/g, ' ')`,
        type: 'success',
        // duration: 5000,
      });
    }

    if (errorMessage) {
      toaster.create({
        title: 'Integration Error',
        description: errorMessage.replace(/_/g, ' '),
        type: 'error',
        // duration: 5000,
      });
    }
  };

  const handleStripeConnect = () => {
    const queryParams = new URLSearchParams({
      response_type: 'code',
      client_id: process.env.NEXT_PUBLIC_STRIPE_CLIENT_ID,
      scope: 'read_write',
      redirect_uri: `${process.env.NEXT_PUBLIC_SITE_URL}/api/stripe/callback`,
      state: user?.id,
    } as any);
    const stripeUrl = `https://connect.stripe.com/oauth/authorize?${queryParams?.toString()}`;
    if (typeof window !== 'undefined') {
      window.location.href = stripeUrl;
    }
  };

  const handleStripeDisconnect = async () => {
    try {
      setStripeLoading(true);
      await fetch('/api/stripe/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stripeUserId: user?.organization?.stripe_user_id,
        }),
      });
      toaster.create({
        type: 'success',
        description: ToastMessages.operationSuccess,
      });
      stripeDisconnectDisclosure.onClose();
      router.replace(
        `/profile?tab=account-settings&success=stripe_disconnect_success`
      );
    } catch (error: any) {
      toaster.create({
        type: 'error',
        description: error?.message || ToastMessages.somethingWrong,
      });
    } finally {
      setStripeLoading(false);
    }
  };

  const handleCalendlyDisconnect = async () => {
    try {
      setCalendlyLoading(true);
      await fetch(
        `/api/calendly/disconnect?organization_id=${user.organization_id}`,
        {
          method: 'DELETE',
        }
      );
      toaster.create({
        type: 'success',
        description: ToastMessages.operationSuccess,
      });
      calendlyDisconnectDisclosure.onClose();
      router.replace(
        `/profile?tab=account-settings&success=calendly_disconnect_success`
      );
    } catch (error: any) {
      toaster.create({
        type: 'error',
        description: error?.message || ToastMessages.somethingWrong,
      });
    } finally {
      setCalendlyLoading(false);
    }
  };

  const handleCalendlyConnect = () => {
    const clientId = process.env.NEXT_PUBLIC_CALENDLY_CLIENT_ID;
    const redirectUri = `${process.env.NEXT_PUBLIC_SITE_URL}/api/calendly/connect`;
    const state = JSON.stringify({
      userId: user.id,
      organization_id: user.organization_id,
    }); // You can add more metadata if needed.

    const authUrl = `https://auth.calendly.com/oauth/authorize?client_id=${clientId}&response_type=code&redirect_uri=${redirectUri}&state=${encodeURIComponent(state)}`;

    window.location.href = authUrl;
  };

  const handleGoogleConnect = async (service: 'gmail' | 'calendar') => {
    const scopeRes = await fetch('/api/auth/google/scopes', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        accessToken: user.google_access_token,
        refreshToken: user?.google_refresh_token,
      }),
    });
    const existingScope = await scopeRes.json();
    console.log('ln 138', scopeRes);

    if (!scopeRes.ok) {
      console.log('ln 140 I am here');

      throw new Error(
        existingScope.error || 'Failed to fetch organization details'
      );
    }
    console.log('existing scopes is ', existingScope);
    // return;

    let newScopes: any = [];

    if (service === 'gmail') {
      newScopes = [
        'https://www.googleapis.com/auth/gmail.modify',
        'email',
        'profile',
        'openid',
      ];
      // scope = `${existingScope.scope} ${scope}`.trim();
    } else if (service === 'calendar') {
      newScopes = [
        'https://www.googleapis.com/auth/calendar.readonly',
        'https://www.googleapis.com/auth/calendar.events',
        'email',
        'profile',
        'openid',
      ];
      // scope = `${existingScope.scope} ${scope}`.trim();
    }
    const existingScopesArray = existingScope?.scope?.split(' ');
    const uniqueNewScopes = newScopes?.filter(
      (scope: any) => !existingScopesArray.includes(scope)
    );

    const finalScopes = [...uniqueNewScopes].join(' ').trim();

    await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${env.FRONTEND_URL}/api/integrations?service=${service}`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
          scope: finalScopes,
          include_granted_scopes: 'true',
        },
      },
    });
  };

  const handleGoogleDisconnect = async () => {
    const payload = {
      is_gmail_connected:
        googleService === 'gmail' ? false : userData.is_gmail_connected,
      is_google_calendar_connected:
        googleService === 'calendar'
          ? false
          : userData.is_google_calendar_connected,
    };
    await mutateAsync(payload);
    await refetch();
    gmailDisconnectDisclosure.onClose();
  };
  const handleKlaviyoConnect = async () => {
    if (userData?.id !== userData?.organization?.owner) return;
    const response = await fetch(
      `/api/auth/klaviyo/connect?organization_id=${user?.organization_id}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.error || 'Failed to initiate Klaviyo OAuth flow'
      );
    }
    const data = await response.json();
    // const authorizeUrl = data.authorizeUrl;
    // window.location.href = authorizeUrl;
    const authorizeUrl = data.authorizeUrl;
    window.open(authorizeUrl, '_blank');
  };

  const handleKlaviyoDisconnect = async () => {
    setDisconnectLoading(true);
    try {
      const response = await fetch('/api/auth/klaviyo/disconnect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ organization_id: userData?.organization_id }),
      });

      if (response.ok) {
        klaviyoDisconnectDisclosure.onClose();
        await refetch();
      } else {
        throw new Error('Failed to disconnect');
      }
    } catch (error) {
      console.error('Failed to disconnect Klaviyo:', error);
      toaster.create({
        description: 'Failed to disconnect Klaviyo. Please try again.',
        type: 'error',
      });
    } finally {
      setDisconnectLoading(false);
    }
  };
  useEffect(() => {
    console.log('i am called ');

    const query = new URLSearchParams(window.location.search);
    handleIntegrationState(query);
  }, []);

  return {
    handleStripeConnect,
    handleStripeDisconnect,
    stripeLoading,
    setStripeLoading,
    // setStripeCanDisconnect,
    stripeDisconnectDisclosure,
    handleCalendlyConnect,
    handleGoogleDisconnect,
    handleCalendlyDisconnect,
    calendlyDisconnectDisclosure,
    calendlyLoading,
    gmailDisconnectDisclosure,
    isLoading,
    handleGoogleConnect,
    setGoogleService,
    googleService,
    userData,
    handleKlaviyoConnect,
    klaviyoDisconnectDisclosure,
    handleKlaviyoDisconnect,
    disconnectLoading,
    editKlaviyoDisclosure,
  };
};
