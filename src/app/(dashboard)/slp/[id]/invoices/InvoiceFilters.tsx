import React from 'react';
import {
  Box,
  HStack,
  Input,
  Badge,
  Flex,
  Text,
  Portal,
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
  MenuPositioner,
} from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import CustomSelect from '@/components/Input/CustomSelect';
import { Colors } from '@/constants/colors';
import SelectContact from '@/components/elements/search/SelectContact';
import { TFilter } from '@/api/invoices/find-by-user';
import DateRange from '@/components/Input/DateRange';
import { InvoiceSummaryData } from '@/api/invoices/get-summary';
import { LuChevronDown, LuFilter, LuCheck } from 'react-icons/lu';

interface InvoiceFiltersProps {
  filters: TFilter;
  updateFilter: (
    keys: keyof TFilter | (keyof TFilter)[],
    values: any | any[]
  ) => void;
  summaryData?: InvoiceSummaryData;
}

const statusOptions = [
  { label: 'All Statuses', value: '' },
  { label: 'Awaiting Payment', value: 'AWAITING_PAYMENT' },
  { label: 'Partially Paid', value: 'PARTIALLY_PAID' },
  { label: 'Paid', value: 'PAID' },
  { label: 'Unpaid', value: 'UNPAID' },
];

export default function InvoiceFilters({
  filters,
  updateFilter,
  summaryData,
}: InvoiceFiltersProps) {
  // Count active filters (excluding default values)
  const activeFilterCount = Object.entries(filters).filter(([key, value]) => {
    if (['limit', 'page', 'org_id'].includes(key)) return false;
    return value && value !== '' && value !== 'all';
  }).length;

  return (
    <Box mb="6">
      {/* Active filters indicator */}
      {activeFilterCount > 0 && (
        <Box mb="4" display={'none'}>
          <HStack gap="2" align="center">
            <Badge
              colorScheme="blue"
              borderRadius="full"
              bg={Colors?.ORANGE?.LIGHT}
              px="2"
              py="1"
              fontSize="xs"
            >
              {activeFilterCount} Active Filters
            </Badge>
          </HStack>
        </Box>
      )}

      {/* Filter Controls - Fully Responsive */}
      <Flex
        direction={{ base: 'column', md: 'row' }}
        gap="4"
        mb="4"
        justify={{ base: 'stretch', md: 'center' }}
        align={{ base: 'stretch', md: 'center' }}
        wrap="wrap"
      >
        {/* Customer Dropdown - responsive width */}
        <Box
          flex={{ base: 'none', md: '1' }}
          minW={{ base: 'full', md: '200px' }}
          maxW={{ base: 'full', md: 'auto' }}
          flexShrink={0}
        >
          <SelectContact
            showNone={true}
            NoneLabel={'All Clients'}
            selectedClient={filters.client_id}
            handleSelectClient={(client: any) => {
              updateFilter('client_id', client.id);
            }}
          />
        </Box>

        {/* Status Dropdown - responsive width */}
        <Box width={{ base: 'full', md: '200px', lg: '180px' }} flexShrink={0}>
          <CustomSelect
            options={statusOptions}
            value={statusOptions.find((opt) => opt.value === filters.status)}
            onChange={(selectedOption) =>
              updateFilter('status', selectedOption?.value)
            }
            placeholder="All Statuses"
            controlStyle={{
              minHeight: '2.5rem',
              height: '2.5rem',
            }}
          />
        </Box>

        {/* Date Range - responsive */}
        <Box
          // flex={{ base: 'none', md: '1' }}
          minW={{ base: 'full', md: '300px' }}
          maxW={{ base: 'full', md: '400px' }}
        >
          <DateRange
            fromValue={filters.date_from}
            toValue={filters.date_to}
            onDateRangeChange={(fromValue, toValue) => {
              updateFilter(['date_from', 'date_to'], [fromValue, toValue]);
            }}
            fromPlaceholder="From"
            toPlaceholder="To"
            height="2.5rem"
            borderRadius="0.5rem"
            border="1px solid #636D79"
          />
        </Box>

        {/* Search Input - responsive width */}
        <Box width={{ base: 'full', md: '250px', lg: '300px' }} flexShrink={0}>
          <Input
            placeholder="Enter Invoice #"
            value={filters.invoice_no}
            onChange={(e) => updateFilter('invoice_no', e.target.value)}
            height="2.5rem"
            borderRadius="0.5rem"
            border="1px solid #636D79"
          />
        </Box>
      </Flex>

      {/* Status Dropdown - Centered */}
      <Flex justify="center" mb="4">
        <MenuRoot positioning={{ placement: 'bottom' }}>
          <MenuTrigger asChild>
            <Button
              variant="outline"
              bg="white"
              border="1px solid #e2e8f0"
              borderRadius="md"
              px="4"
              py="2"
              minW="200px"
              justifyContent="space-between"
              _hover={{ bg: 'gray.50' }}
            >
              <Flex align="center" gap="2">
                <LuFilter size={16} />
                <Text fontSize="sm" fontWeight="medium">
                  {filters.status === 'UNPAID'
                    ? 'Unpaid Invoices'
                    : 'All Invoices'}
                </Text>
                <Badge
                  bg={Colors?.ORANGE?.PRIMARY}
                  color="white"
                  borderRadius="full"
                  px="2"
                  py="0.5"
                  fontSize="xs"
                  ml="1"
                >
                  {filters.status === 'UNPAID'
                    ? summaryData?.statusCounts?.unpaid || 0
                    : summaryData?.statusCounts?.total || 0}
                </Badge>
              </Flex>
              <LuChevronDown size={16} />
            </Button>
          </MenuTrigger>
          <Portal>
            <MenuPositioner>
              <MenuContent minW="200px">
                <MenuItem
                  value="unpaid"
                  onClick={() => updateFilter('status', 'UNPAID')}
                  bg={filters.status === 'UNPAID' ? 'gray.50' : 'transparent'}
                >
                  <Flex justify="space-between" align="center" w="full">
                    <Flex align="center" gap="2">
                      <Box w={'16px'} h={'16px'} minW={'16px'} minH={'16px'}>
                        {' '}
                        {filters.status === 'UNPAID' && (
                          <LuCheck size={16} color={Colors?.ORANGE?.PRIMARY} />
                        )}
                      </Box>
                      <Text fontSize="sm">Unpaid Invoices</Text>
                    </Flex>
                    <Badge
                      bg={Colors?.ORANGE?.PRIMARY}
                      color="white"
                      borderRadius="full"
                      px="2"
                      py="0.5"
                      fontSize="xs"
                    >
                      {summaryData?.statusCounts?.unpaid || 0}
                    </Badge>
                  </Flex>
                </MenuItem>
                <MenuItem
                  value="all"
                  onClick={() => updateFilter('status', '')}
                  bg={
                    !filters.status || filters.status === ''
                      ? 'gray.50'
                      : 'transparent'
                  }
                >
                  <Flex justify="space-between" align="center" w="full">
                    <Flex align="center" gap="2">
                      <Box w={'16px'} h={'16px'} minW={'16px'} minH={'16px'}>
                        {(!filters.status || filters.status === '') && (
                          <LuCheck size={16} color={'gray.500'} />
                        )}
                      </Box>
                      <Text fontSize="sm">All Invoices</Text>
                    </Flex>
                    <Badge
                      bg={Colors?.GRAY?.LIGHT}
                      color="black"
                      borderRadius="full"
                      px="2"
                      py="0.5"
                      fontSize="xs"
                    >
                      {summaryData?.statusCounts?.total || 0}
                    </Badge>
                  </Flex>
                </MenuItem>
              </MenuContent>
            </MenuPositioner>
          </Portal>
        </MenuRoot>
      </Flex>
    </Box>
  );
}
