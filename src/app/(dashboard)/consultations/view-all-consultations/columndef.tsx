import { IClient } from '@/shared/interface/clients';
import { IBookings } from '@/shared/interface/consultation';
import { changeProvinceShortForm } from '@/utils/province-helper';
import { Box } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import 'moment-timezone';
import 'moment/locale/en-ca';
import moment from 'moment/moment';
import Link from 'next/link';
import ConsultationAction from './ConsultationAction';
import { dateSortingFn } from '@/utils/sort';
export interface IConsultationsDisplay extends IBookings {
  clients: IClient;
}
const columnHelper = createColumnHelper<
  IConsultationsDisplay & { name: string }
>();

export const columnDef = [
  columnHelper.accessor('name', {
    id: 'name',
    enableSorting: true,
    header: 'Name',
    cell: (props) => {
      return (
        <Link href={'/contacts/' + props.row.original?.client_id}>
          <Box color={'rgb(79 70 229)'} fontWeight={500}>
            {props.getValue()}
          </Box>
        </Link>
      );
    },
  }),
  columnHelper.accessor('clients.phone', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Phone',
    id: 'phone',
  }),

  columnHelper.accessor('email', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Email',
    id: 'email',
    enableSorting: true,
  }),

  columnHelper.accessor('province', {
    cell: (info) => <Box>{changeProvinceShortForm(info.getValue())}</Box>,
    header: 'Province',
    id: 'province',
    enableSorting: true,
  }),
  columnHelper.accessor('clients.stage', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Stage',
    id: 'stage',
    enableSorting: true,
  }),
  columnHelper.accessor('appointment', {
    cell: (info) => <Box>{moment(info.getValue()).format('h:mma')}</Box>,
    header: 'Booking (EST)',
    id: 'booking-est',
    enableSorting: true,
    sortingFn: dateSortingFn,
  }),
  columnHelper.display({
    id: 'actions',
    cell: (props) => <ConsultationAction row={props.row?.original} />,
    // header: 'Actions',
  }),
];
