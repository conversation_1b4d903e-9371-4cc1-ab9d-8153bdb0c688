import { useGetAllBookingsQuery } from '@/api/bookings/get-all-bookings';
import { getPrimaryEmail } from '@/utils/helper';
import { useEffect, useState } from 'react';

export const useBookingsHook = () => {
  const [event, setEvent] = useState<any>([]);
  const [page_size, setPageSize] = useState(50);
  const [current_page, setCurrentPage] = useState(1);
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState(search);

  // Debounce logic
  useEffect(() => {
    const handler = setTimeout(() => {
      const cleanedSearch = search.trim().replace(/\s/g, '');
      setDebouncedSearch(cleanedSearch);
      //setDebouncedSearch(search.trim().replace(/\s/g, ''));
    }, 800);

    return () => clearTimeout(handler);
  }, [search]);

  //const email = debouncedSearch?.length > 3 ? debouncedSearch : '';
  const email = debouncedSearch;

  const { data, isLoading, refetch } = useGetAllBookingsQuery({
    event,
    page_size,
    current_page,
    email,
  });

  const eventOptions = [
    { label: 'Package', value: 'package' },
    { label: 'Session', value: 'session' },
    { label: 'Consultation', value: 'consultation' },
    { label: 'Others', value: 'others' },
  ];

  // Handle different data structures
  const getRawData = () => {
    if (!data) return [];

    // Check if data has nested structure (normal booking data)
    if (data.data && Array.isArray(data.data)) {
      return data.data;
    }

    // Check if data is directly an array (search results)
    if (Array.isArray(data)) {
      return data;
    }

    return [];
  };

  const getTotalCount = () => {
    if (!data) return 0;

    // Normal booking data with total_count
    if (data.total_count) {
      return data.total_count;
    }

    // Search results - count the array length
    const rawData = getRawData();
    return rawData.length;
  };

  const finalData = {
    total_count: getTotalCount(),
    data: getRawData().map((item: any) => ({
      ...item,
      name:
        item?.display_name ||
        (item?.first_name && item?.last_name
          ? `${item?.first_name} ${item?.last_name}`
          : `${item.first_name ?? '_'} ${item.last_name ?? ''}`),
      email: getPrimaryEmail(item?.emails || []) || item?.email || '',
      event: item?.service?.name || item?.event,
    })),
  };

  // const finalData = {
  //   ...data,
  //   data: data?.data?.map((item: any) => ({
  //     ...item,
  //     name:
  //       item?.display_name ||
  //       (item?.first_name && item?.last_name
  //         ? `${item?.first_name} ${item?.last_name}`
  //         : `${item.first_name ?? ''} ${item.last_name ?? ''}`),
  //     email: getPrimaryEmail(item?.emails || []) || item?.email || '',
  //     event: item?.service?.name || item?.event,
  //   })),
  // };
  return {
    search,
    setSearch,
    setCurrentPage,
    setEvent,
    event,
    setPageSize,
    isLoading,
    current_page,
    page_size,
    eventOptions,
    refetch,
    data: finalData,
  };
};
