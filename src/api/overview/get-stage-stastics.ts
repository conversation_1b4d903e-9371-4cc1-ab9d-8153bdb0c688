//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getStageStatistics() {
  const response = await fetch(`/api/overview/stages`, { method: 'GET' });
  const json = await response.json();
  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch stats');
  }
  return json;
}

type QueryFnType = typeof getStageStatistics;

export const useGetStageStatsQuery = (
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.overview.stageStats],
    queryFn: getStageStatistics,
    ...config,
  });
};
