export const revalidate = 0;

import {
  // generateAvailability,
  getOrganizationByOrganizationSlug,
} from '@/app/service/events';
import { getFormBySlug } from '@/app/service/forms';
import soapLogo from '@/assets/soapLogo.png';
import { env } from '@/constants/env';
import { Box, Flex, Image, Text } from '@chakra-ui/react';
import { createClient } from '@supabase/supabase-js';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import CreateForm from './_components/CreateForm';

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
//

export async function generateMetadata({
  params,
}: {
  params: { organization_slug: string; form_slug: string };
}): Promise<Metadata> {
  console.log('params', params);
  const organizationDetails = await getOrganizationByOrganizationSlug(
    supabase,
    params.organization_slug
  );
  const formDetails = await getFormBySlug(supabase, params?.form_slug);

  if (!organizationDetails) {
    return {
      title: 'Organization Not Found',
      description: 'The specified organization does not exist.',
    };
  }

  if (!formDetails) {
    return {
      title: 'Form Not Found',
      description: 'The specified form does not exist.',
    };
  }

  const fullTitle = `Submit ${formDetails?.title} with ${organizationDetails?.name} | Soap`;
  const description = `Create a ${formDetails?.title} form with ${organizationDetails?.name}.`;

  return {
    title: fullTitle,
    description,
    openGraph: {
      title: fullTitle,
      description,
      url: `https://app.soapnotes.online/${organizationDetails?.organization_name}/${params?.form_slug}`,
      siteName: 'Soap Dashboard',
      images: [
        {
          url: 'https://app.soapnotes.online/og-image.png',
          width: 1200,
          height: 630,
          alt: 'Soap Form Submission',
        },
      ],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: ['https://app.soapnotes.online/twitter-image.png'],
    },
    metadataBase: new URL('https://app.soapnotes.online'),
  };
}

export default async function page({ params }: any) {
  // console.log('In public facin page , params is ', params);

  // const organizationDetails = await getOrganizationByName(
  //   supabase,
  //   params.organization_name
  // );
  const organizationDetails = await getOrganizationByOrganizationSlug(
    supabase,
    params.organization_slug
  );
  const formDetails = await getFormBySlug(supabase, params.form_slug);

  // console.log('formDetails', formDetails);
  console.log('params', params);

  // const formDetails = await getEventsBySlug(supabase, params.form_slug);
  // const availability = await generateAvailability(supabase, params.event_slug);

  if (!organizationDetails) {
    notFound();
  }
  if (!formDetails) {
    notFound();
  }

  console.log('formDetails', formDetails);
  console.log('organizationDetails', organizationDetails);
  return (
    <Box minH={'100vh'} pt={'2rem'} w={{ base: '95%', md: '100%' }}>
      <Box
        rounded={'.5rem'}
        boxShadow={'lg'}
        w={'full'}
        minW={{ base: '95%', md: '500px' }}
        maxW={{ md: '600px', base: '95%' }}
        mx={'auto'}
        overflowY={'auto'}
        maxH={'95vh'}
        bg={'white'}
      >
        <Flex
          flexDirection={'column'}
          alignItems={'start'}
          gap={'1rem'}
          p={'1rem'}
        >
          <Flex
            justifyContent={'space-between'}
            w={'100%'}
            alignItems={'center'}
          >
            {!formDetails?.logo_url && (
              <Text
                fontSize={{ md: '1.5rem', base: '0.9rem' }}
                fontWeight={'bold'}
              >
                {organizationDetails?.name}
              </Text>
            )}

            <Box
              w="12rem"
              h="6rem"
              padding="1rem"
              display="flex"
              justifyContent="center"
              alignItems="center"
            >
              <Image
                src={formDetails?.logo_url ?? soapLogo.src}
                alt="logo"
                w="100%"
                h="100%"
                borderRadius="md"
                objectFit={'contain'}
              />
            </Box>
          </Flex>

          {/* <Box
              display={'flex'}
              textAlign={'center'}
              flexDirection={'column'}
              alignItems={'center'}
              justifyContent={'center'}
              width={'100%'}
              textTransform={'capitalize'}
            >
              <Text fontSize={'1.3rem'} fontWeight={'semibold'}>
                {formDetails?.title}
              </Text>
              <Text fontSize="sm" color="gray.500" mb={3} lineHeight="short">
                {formDetails.description}
              </Text>
            </Box> */}

          {/* <Box
              display={'flex'}
              textAlign={'center'}
              justifyContent={'center'}
              width={'100%'}
            >
              <Text fontSize={'1rem'} fontWeight={'light'}>
                {formDetails?.header_message || ''}
              </Text>
            </Box> */}
        </Flex>

        <CreateForm formDetails={formDetails} />
      </Box>
    </Box>
  );
}
