import { Box, Center, Flex, Stack, Text } from '@chakra-ui/react';
import Link from 'next/link';
import { FiMail } from 'react-icons/fi';
import { GoDotFill } from 'react-icons/go';

const formatEmailAddress = (email: string | undefined): string => {
  if (!email) return '';

  if (email.includes(',')) {
    return email
      .split(',')
      .map((e) => {
        const match = e.trim().match(/<([^>]+)>/) || [null, e.trim()];
        return match[1];
      })
      .join(', ');
  }

  const match = email.match(/<([^>]+)>/) || [null, email];
  return match[1];
};

export default function EmailCreated({ data }: any) {
  // console.log('ln 25', data);

  return (
    <Link href={`?tab=email`}>
      <Box w={'100%'} h={'fit'}>
        <Flex w={'full'} direction={'row'}>
          <Stack maxW={'3rem'} minW={'3rem'} gap={0} alignItems={'center'}>
            <GoDotFill color="#000" />
            <Box minH={'1rem'} bg={'#dee1e0'} h={'100%'} w={'.5px'}></Box>
          </Stack>
          <Flex
            w={'full'}
            maxW={'960px'}
            flexWrap={'wrap'}
            gap={'2'}
            direction={'column'}
            mb={'4'}
          >
            <Box>
              <Flex direction={'column'} gap={'1.5'}>
                {/* <Text fontSize={'12px'} color="#7C7C7C">
                  {moment(data?.details?.date).format('MMMM D, YYYY')}
                </Text> */}
                <Flex justifyContent={'space-between'}>
                  <Center
                    fontSize={'14px'}
                    fontWeight={'500'}
                    textTransform={'capitalize'}
                    lineClamp={'2'}
                  >
                    {data?.activity_type === 'email_sent'
                      ? 'Email Sent'
                      : 'Email Received'}{' '}
                    : {data?.details?.subject}
                  </Center>
                </Flex>
              </Flex>
            </Box>
            <Box
              rounded={'4.8px'}
              border={'.0625rem solid #dee1e0'}
              py={'0.8rem'}
              px={'8px'}
              display={'flex'}
              alignItems={'center'}
              w={'100%'}
              //maxHeight={'5rem'}
              minHeight={'3.5rem'}
              //height={'3x.5rem'}
              gap={'1rem'}
              cursor={'pointer'}
              _hover={{
                boxShadow: 'lg',
                transition: 'all 0.2s ease-in-out',
              }}
            >
              <Box
                rounded={'4.8px'}
                fontSize={'16px'}
                display={'flex'}
                justifyContent={'center'}
                alignItems={'center'}
                minW={'36px'}
                w={'36px'}
                minH={'36px'}
                maxH={'36px'}
                maxW={'36px'}
                cursor={'pointer'}
                bg={'green.50'}
                color={'green.600'}
              >
                <FiMail />
              </Box>

              <Box
                textAlign={'left'}
                display={'flex'}
                flexDirection={'column'}
                //fontWeight={'500'}
                fontSize={'14px'}
                gap={'1'}
              >
                <Text>To: {formatEmailAddress(data?.details?.to)}</Text>
                <Stack gap={'-1.5'}>
                  {data?.details.cc && (
                    <Text lineClamp={1}>
                      Cc: {formatEmailAddress(data?.details?.cc)}
                    </Text>
                  )}

                  {data?.details.bcc && (
                    <Text lineClamp={1}>
                      Bcc: {formatEmailAddress(data?.details?.bcc)}
                    </Text>
                  )}
                </Stack>

                <Box
                  textAlign={'left'}
                  display={'flex'}
                  flexDirection={'row'}
                  //fontSize={'12px'}
                >
                  <Text>From: {formatEmailAddress(data?.details?.from)}</Text>
                </Box>
              </Box>
            </Box>
          </Flex>
        </Flex>
      </Box>
    </Link>
  );
}

{
  /* <Flex maxW={'60rem'} flexWrap={'wrap'}>
  <Box
    rounded={'.3rem'}
    border={'1px solid #eef1f0'}
    py={'.8rem'}
    px={'.5rem'}
    w={'100%'}
  >
    <Flex justifyContent={'space-between'}>
      <Center
        py={'.1rem'}
        px={'.5rem'}
        rounded={'.2rem'}
        bg={'#32CD32'}
        fontSize={'.8rem'}
        color={'white'}
        gap={'1.5'}
      >
        <GoNote size={'1rem'} color="#dee1e0" />
        {data?.activity_type === 'email_sent' ? 'Email Sent' : 'Email Received'}
      </Center>
      <Stack>
        <Text fontSize="xs" color="gray.500">
          to: {formatEmailAddress(data?.details?.to)}
        </Text>
        <Text fontSize="xs" color="gray.400">
          from: {formatEmailAddress(data?.details?.from)}
        </Text>
      </Stack>
      <Text ml={'2rem'} fontSize={'.75rem'} color="#7C7C7C">
        {moment(data?.details?.date).format('MMMM D, YYYY')}
      </Text>
    </Flex>
    <Box textAlign={'left'} mt={'.7rem'} fontSize={'.75rem'}>
      {data?.details?.subject}
    </Box>
  </Box>
</Flex>; */
}
