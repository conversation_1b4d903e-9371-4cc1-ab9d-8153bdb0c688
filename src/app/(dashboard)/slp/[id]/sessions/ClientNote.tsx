// import AnimateLoader from '@/components/elements/loader/animate-loader';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
// import { useRouter } from 'next/navigation';
import React from 'react';
import moment from 'moment/moment';
import { ClientNoteHook } from '@/hooks/admin/notes/useNoteHook';

export default function ClientNote({
  clientNoteHook,
}: {
  clientNoteHook: ClientNoteHook;
}) {
  // const router = useRouter();

  // const handleViewMoreClick = () => {
  //   router.push(`/contacts/${selectedClientId || currentClient?.id}#SOAP`);
  // };

  return (
    <Box w={'100%'}>
      <Text textAlign={'center'} my={'1rem'}>
        {/* Client Notes */}
      </Text>

      <Stack w={'100%'}>
        {clientNoteHook?.data?.length > 0
          ? clientNoteHook?.data
              ?.sort(
                (a: any, b: any) =>
                  new Date(b.note_date).getTime() -
                  new Date(a.note_date).getTime()
              )
              .map((item: any) => (
                <Flex
                  key={item.created_at}
                  borderBottom={'1px solid rgba(0,0,0,.2)'}
                  pb={'1rem'}
                  justifyContent={'space-between'}
                  alignItems={'flex-end'}
                >
                  <Box>
                    <Text fontWeight={'bold'}>{item?.title}</Text>
                    <Box
                      display={'flex'}
                      flexDir={'column'}
                      gap={'.3rem'}
                      className="noteBox"
                      dangerouslySetInnerHTML={{ __html: item.notes }}
                    ></Box>

                    <Text color={'gray.300'} fontSize={'.8rem'} mt={'.5rem'}>
                      {moment(item.note_date).format('MMM D, YYYY')}
                      {/* <span>{`${item?.slp_id?.first_name} ${item?.slp_id?.last_name}`}</span> */}
                    </Text>
                  </Box>
                </Flex>
              ))
          : null}
        {/* {isSlp && SoapNotes?.length >= 5 && (
          <Box onClick={handleViewMoreClick} cursor={'pointer'}>
            <Text color={'#e9485b'}>View More</Text>
          </Box>
        )} */}
      </Stack>
    </Box>
  );
}
