'use client';

import AnimateLoader from '@/components/elements/loader/animate-loader';
import CustomTextArea from '@/components/Input/CustomTextArea';
import StringInput from '@/components/Input/StringInput';
import { env } from '@/constants/env';
import { useFormHook } from '@/hooks/forms/useFormHook';
import { getSlugFromName } from '@/utils/event';
import {
  Box,
  Button,
  Center,
  Flex,
  Heading,
  Input,
  Separator,
  Spinner,
  Stack,
  Text,
  VStack,
} from '@chakra-ui/react';
import { CiTextAlignRight } from 'react-icons/ci';
import { LiaTimesSolid } from 'react-icons/lia';
import Questions from '../../create/Questions';

const EditForm = ({
  id,
  getFormsViewState,
}: {
  id: any;
  getFormsViewState?: any;
}) => {
  const formHook = useFormHook({ id });
  const {
    loading,
    values,
    errors,
    touched,
    FormDataIsloading,
    handleChange,
    handleSubmitNewForm,
    setFieldValue,
  } = formHook;

  const raw = localStorage.getItem('UserState');
  const data = raw ? JSON.parse(raw) : null;
  const org = data?.UserState?.organization;

  if (FormDataIsloading) {
    return (
      <Center h={'20rem'}>
        <AnimateLoader />
      </Center>
    );
  }

  return (
    <Box
      display={'flex'}
      flexDirection={'column'}
      width={{ base: '95%', lg: '80%' }}
      mx={'auto'}
      px={{ base: 4, lg: 0 }}
    >
      {/* Header - Mobile Optimized */}
      <Flex
        direction={{ base: 'column', md: 'row' }}
        align={{ base: 'stretch', md: 'center' }}
        justify={{ base: 'flex-start', md: 'space-between' }}
        width={'100%'}
        gap={{ base: 4, md: 0 }}
      >
        <Flex alignItems={'center'} spaceX={'10px'}>
          <Box
            onClick={() => getFormsViewState('dashboard')}
            borderRadius={'10px'}
            p={'10px'}
            cursor={'pointer'}
            _hover={{ bg: '#d96847', color: '#fff' }}
            bg={'#F2F2F2'}
            flexShrink={0}
          >
            <LiaTimesSolid />
          </Box>

          <Stack spaceY={1}>
            <Heading
              fontWeight={400}
              fontSize={{ base: '1.2rem', lg: '1.5rem' }}
            >
              Edit Form Template
            </Heading>
            <Text
              display={'flex'}
              gap={'5px'}
              alignItems={'center'}
              fontSize={{ base: '0.8rem', lg: '1rem' }}
              color="gray.600"
            >
              <CiTextAlignRight /> Form Template
            </Text>
          </Stack>
        </Flex>

        <Button
          onClick={handleSubmitNewForm}
          bg={'primary.500'}
          width={{ base: 'full', md: '8rem' }}
          height="40px"
        >
          {loading ? <Spinner size={'sm'} /> : 'Save Changes'}
        </Button>
      </Flex>

      {/* Details Section - Mobile Optimized */}
      <Stack
        direction={{ base: 'column', lg: 'row' }}
        spaceY={{ base: 4, lg: 0 }}
        spaceX={{ base: 0, lg: 8 }}
        align={{ base: 'stretch', lg: 'flex-start' }}
        mt={'3rem'}
        width={'100%'}
      >
        {/* Section Title */}
        <Text
          width={{ base: 'full', lg: '20%' }}
          fontSize={{ base: 'lg', lg: 'md' }}
          fontWeight={{ base: '600', lg: '400' }}
          mb={{ base: 0, lg: 0 }}
        >
          Details
        </Text>

        {/* Form Fields */}
        <VStack spaceY={6} width={{ base: 'full', lg: '70%' }} align="stretch">
          {/* Title Field */}
          <StringInput
            inputProps={{
              name: 'title',
              type: 'text',
              placeholder: 'Enter a title',
              value: values.title,
              onChange: (e) => {
                handleChange(e);
                setFieldValue('slug', getSlugFromName(e.target.value));
              },
            }}
            fieldProps={{
              label: 'Title',
              invalid: touched.title && !!errors.title,
              errorText: errors.title,
              required: true,
            }}
          />

          {/* URL Field - Always Stacked */}
          <Box width="full">
            <Text fontWeight={'500'} mb={3} fontSize={{ base: 'sm', lg: 'md' }}>
              Form URL
              <Text as="sup" fontSize={'15px'} color="red.500">
                *
              </Text>
            </Text>

            <VStack spaceY={3} w="full" align="stretch">
              {/* Base URL Display - Never shrinks */}
              {/* <Box
                bg="gray.100"
                px={4}
                py={0}
                borderRadius="md"
                border="1px solid"
                borderColor="gray.200"
                height="44px"
                display="flex"
                alignItems="center"
                w="full"
                minW="fit-content"
                overflow="hidden"
              >
                <Text
                  fontSize={{ base: 'xs', lg: 'sm' }}
                  color="gray.600"
                  whiteSpace="nowrap"
                  title={`${env.FRONTEND_URL}/forms/${org?.slug}/`}
                  overflow="hidden"
                  textOverflow="ellipsis"
                  w="full"
                >
                  {env.FRONTEND_URL}/forms/{org?.slug}/
                </Text>
              </Box> */}

              {/* Slug Input */}
              <Input
                name="slug"
                type="text"
                value={values.slug}
                onChange={(e: any) => {
                  handleChange({
                    target: {
                      name: 'slug',
                      value: e.target.value,
                    },
                  });
                }}
                placeholder="enter-url-slug"
                height="44px"
                borderRadius="md"
                w="full"
                fontSize={{ base: 'sm', lg: 'md' }}
                _focus={{
                  borderColor: 'black.500',
                  boxShadow: '0 0 0 1px var(--chakra-colors-black-500)',
                }}
              />
            </VStack>

            {/* Full URL Preview */}
            <Box
              mt={3}
              p={3}
              bg="primary.50"
              borderRadius="md"
              border="1px solid"
              borderColor="primary.200"
            >
              <Text fontSize="xs" color="primary.600" wordBreak="break-all">
                <Text as="span" fontWeight="medium">
                  Preview:{' '}
                </Text>
                {env.FRONTEND_URL}/forms/{org?.slug}/
                {values.slug || 'your-form-slug'}
              </Text>
            </Box>

            {errors.slug && (
              <Text color="red.500" fontSize="xs" mt={2}>
                {errors.slug}
              </Text>
            )}
          </Box>

          {/* Description Field */}
          <CustomTextArea
            inputProps={{
              name: 'description',
              value: values.description,
              onChange: handleChange,
            }}
            fieldProps={{
              label: 'Form Description',
              invalid: touched.description && !!errors.description,
              errorText: errors.description,
            }}
          />
        </VStack>
      </Stack>

      <Separator my={'2rem'} />

      {/* Questions Section */}
      <Box width={'100%'}>
        <Questions formHook={formHook} />
      </Box>
    </Box>
  );
};

export default EditForm;
