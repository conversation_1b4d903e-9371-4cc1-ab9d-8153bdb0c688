//OPTIMIZED
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { MutationConfig, useMutation } from '@/lib/react-query';

const updateSlpNote = async (body: { data: any; id: number }) => {
  console.log('Updating SLP note with body:', body);
  const response = await fetch(`/api/slp-notes/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify({ ...body.data }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating slp note');
  }
  return response.json();
};

type QueryFnType = typeof updateSlpNote;

export const useUpdateSlpNoteMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toast({
    //     status: 'success',
    //     description: ToastMessages.operationSuccess,
    //   });
    // },
    retry: false,
    mutationKey: [queryKey.slpNotes.updateSlpNote],
    mutationFn: updateSlpNote,
    ...config,
  });
};
