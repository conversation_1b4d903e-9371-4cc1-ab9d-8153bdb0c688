import { NextRequest, NextResponse } from 'next/server';
// import supabase from '@/lib/supabase/client';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getArrayParam, getNumberParam } from '@/utils/format-object';
import { KlaviyoAPI } from '@/lib/klaviyo/api';
import { KlaviyoActions } from '@/constants/klaviyo-actions';
import { KlaviyoTokens } from '@/lib/klaviyo/oauth';

export async function GET(request: NextRequest) {
  const supabase = createSupabaseServer();

  const { searchParams } = new URL(request.url);
  const event = getArrayParam(searchParams, 'event');
  const page_size = getNumberParam(searchParams, 'page_size', 50);
  const current_page = getNumberParam(searchParams, 'current_page', 1);
  const search = searchParams.get('email') || '';
  const org_id = searchParams.get('organization_id') || '';

  const organization_id = Number(org_id);

  const isEmail = Boolean(search.length > 0);

  const query = isEmail
    ? supabase
        .from(tableNames.bookings)
        .select('*, clients:client_id(*), services:service_id(*)')
        .eq('organization_id', organization_id)
        .ilike('email', `%${search}%`)
    : supabase.rpc('get_filtered_bookings', {
        event_filter: event,
        current_page,
        page_size,
        organization_id,
      });

  // Supabase RPC call
  const { data, error } = await query;

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 200 });
}

export async function POST(request: NextRequest) {
  const body = await request.json();
  const supabase = createSupabaseServer();
  const { payload, event, organization_id } = body;

  const { data, error } = await supabase
    .from(tableNames.bookings)
    .insert(payload)
    .select();

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }
  if (event) {
    try {
      const { data: KlaviyoDetails, error: DetailsError } = await supabase
        .from(tableNames.klaviyo_organization)
        .select('*')
        .eq('organization_id', organization_id)
        .maybeSingle();
      if (DetailsError) throw DetailsError;

      const handleRefreshToken = async (tokens: KlaviyoTokens) => {
        await supabase
          .from(tableNames.klaviyo_organization)
          .update({
            access_token: tokens.access_token,
            refresh_token: tokens.refresh_token,
            updated_at: new Date(),
          })
          .eq('organization_id', organization_id);
      };

      const klaviyoApi = new KlaviyoAPI(
        KlaviyoDetails?.access_token,
        KlaviyoDetails?.refresh_token,
        handleRefreshToken
      );
      // console.log('event is ', event);
      await klaviyoApi.trackEvent(
        organization_id,
        KlaviyoActions.BOOKING_CREATED,
        event,
        supabase
      );
      // console.log('res is ', res);
      // return;
    } catch (klaviyoError: any) {
      console.warn('Failed to track Klaviyo event in route:', klaviyoError);
      // Continue even if Klaviyo fails to avoid blocking booking creation
      // return NextResponse.json(
      //   { message: klaviyoError.message },
      //   { status: 500 }
      // );
    }
  }
  return NextResponse.json(data?.[0], { status: 200 });
}
