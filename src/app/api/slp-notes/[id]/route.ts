import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
// import supabase from '@/lib/supabase/client';
import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  console.log('Fetching SLP notes for client ID:', params.id);
  try {
    const supabase = createSupabaseServer();

    const { id } = params;
    const { searchParams } = new URL(request.url);
    const linkedClientId = searchParams.get('linkedClientId') || '';

    let query = supabase
      .from(tableNames.slp_notes)
      .select(`*, slp_id(*)`)
      .eq('client_id', Number(id))
      .order('created_at', { ascending: false });
    // .limit(10);

    if (linkedClientId === null) {
      query = query.is('linked_client_id', null);
    } else if (linkedClientId) {
      query = query.eq('linked_client_id', linkedClientId);
    }
    const { data, error } = await query;
    if (error) {
      throw error;
    }

    return NextResponse.json(data);
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Updating SLP note for ID:', params.id);
    const supabase = createSupabaseServer();

    const { id } = params;

    const payload = await request.json();

    if (!id || !payload) {
      return NextResponse.json(
        { message: 'ID and data are required' },
        { status: 400 }
      );
    }
    await supabase
      .from(tableNames.slp_notes)
      .update(payload)
      .eq('id', Number(id));

    return NextResponse.json({ message: 'Updated' });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
