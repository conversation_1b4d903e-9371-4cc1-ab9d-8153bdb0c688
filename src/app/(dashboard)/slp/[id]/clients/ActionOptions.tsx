import React from 'react';
import {
  Box,
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuSeparator,
  MenuTrigger,
} from '@chakra-ui/react';
import { BsThreeDotsVertical } from 'react-icons/bs';

const ActionOptions = ({ data }: { data: any }) => {
  return (
    <Box position={'relative'}>
      <MenuRoot>
        <MenuTrigger cursor={'pointer'}>
          <BsThreeDotsVertical />
        </MenuTrigger>
        <MenuContent
          cursor={'pointer'}
          position={'absolute'}
          right={0}
          left={'auto'}
        >
          <MenuItem value="edit">
            <Box>Edit</Box>
          </MenuItem>
          <MenuSeparator />
          <MenuItem value="delete">
            <Box>Delete</Box>
          </MenuItem>
        </MenuContent>
      </MenuRoot>
    </Box>
  );
};

export default ActionOptions;
