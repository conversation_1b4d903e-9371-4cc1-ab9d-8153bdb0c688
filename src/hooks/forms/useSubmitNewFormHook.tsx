/* eslint-disable react-hooks/exhaustive-deps */

import {
  useCreateAnswersMutation,
  useCreateClientActivitiesMutation,
  useCreateClientEmailsMutation,
  useCreateClientMutation,
  useUpdateAnswersMutation,
  useUpdateClientActivitiesMutation,
} from '@/api/forms/upload-form-answers';
import { useAddNotableDateApi } from '@/api/notable_date/add-notable-date';
import { toaster } from '@/components/ui/toaster';
import { useFormik } from 'formik';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import * as Yup from 'yup';

// Define proper types for better type safety
interface Question {
  id: string;
  page: number;
  type: string;
  qt: string;
  required: boolean;
  default?: string;
  notableDate?: boolean;
  event?: string;
  other?: string;
}

interface FormDetails {
  id: string;
  title: string;
  organization_id: string;
  organization_name: string;
  user_id: string;
  slug: string;
  questions: Question[];
}

// Cache management utilities
const getCacheKey = (formId: string) => `form_cache_${formId}`;
const getProgressKey = (formId: string) => `form_progress_${formId}`;

interface FormCache {
  allFormData: Record<string, any>;
  currentPage: number;
  submissionId: string | null;
  submittedPages: number[];
  clientData: any;
  lastSaved: string;
}

const saveFormCache = (formId: string, cache: FormCache) => {
  try {
    localStorage.setItem(getCacheKey(formId), JSON.stringify(cache));
  } catch (error) {
    console.log('Failed to save form cache:', error);
  }
};

const loadFormCache = (formId: string): FormCache | null => {
  try {
    const cached = localStorage.getItem(getCacheKey(formId));
    return cached ? JSON.parse(cached) : null;
  } catch (error) {
    console.log('Failed to load form cache:', error);
    return null;
  }
};

const clearFormCache = (formId: string) => {
  try {
    localStorage.removeItem(getCacheKey(formId));
    localStorage.removeItem(getProgressKey(formId));
  } catch (error) {
    console.log('Failed to clear form cache:', error);
  }
};

const useMultiPageFormHook = ({
  formDetails,
}: {
  formDetails: FormDetails;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [submissionId, setSubmissionId] = useState<string | null>(null);
  const [submittedPages, setSubmittedPages] = useState<Set<number>>(new Set());
  const [clientData, setClientData] = useState<any>(null);
  const [allFormData, setAllFormData] = useState<Record<string, any>>({});
  const [hasCachedDataFromStorage, setHasCachedDataFromStorage] =
    useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [formKey, setFormKey] = useState(0); // Add key to force form recreation
  const router = useRouter();

  // console.log('formDetails', formDetails);

  const [disableButton, setDisableButton] = useState(false);

  const { mutateAsync: createAnswers } = useCreateAnswersMutation();
  const { mutateAsync: updateAnswers } = useUpdateAnswersMutation();
  const { mutateAsync: updateClientActivities } =
    useUpdateClientActivitiesMutation();
  const { mutateAsync: createClientEmails } = useCreateClientEmailsMutation();
  const { mutateAsync: createClientActivities } =
    useCreateClientActivitiesMutation();

  const { mutateAsync: createClient } = useCreateClientMutation();
  const { mutateAsync: createNotableDateAsync } = useAddNotableDateApi();

  // Get unique pages from questions - now properly typed
  const pages = useMemo(() => {
    const pageNumbers = [
      ...new Set(formDetails.questions.map((q: Question) => q.page)),
    ].sort((a: number, b: number) => a - b);
    return pageNumbers;
  }, [formDetails.questions]);

  // Get questions for current page
  const currentPageQuestions = useMemo(() => {
    return formDetails.questions.filter(
      (q: Question) => q.page === currentPage
    );
  }, [formDetails.questions, currentPage]);

  // Initialize form state synchronously on mount
  useEffect(() => {
    const initializeForm = () => {
      // Load cached data synchronously
      const cached = loadFormCache(formDetails.id);

      if (cached && Object.keys(cached.allFormData).length > 0) {
        // Restore from cache
        setAllFormData(cached.allFormData);
        setCurrentPage(cached.currentPage);
        setSubmissionId(cached.submissionId);
        setSubmittedPages(new Set(cached.submittedPages));
        setClientData(cached.clientData);
        setHasCachedDataFromStorage(true);
      }

      setIsInitialized(true);
    };

    initializeForm();
  }, [formDetails.id]);

  // Create initial values for current page only - only after initialization
  const initialValues = useMemo(() => {
    const values: any = {};
    currentPageQuestions.forEach((question: Question) => {
      const cachedValue = allFormData[`question_${question.id}`];
      values[`question_${question.id}`] =
        cachedValue !== undefined
          ? cachedValue
          : question.type === 'Multiple choice'
            ? []
            : '';
    });
    return values;
  }, [currentPageQuestions, allFormData]);

  // Create validation schema for current page only
  const validationSchema = useMemo(() => {
    const schema: any = {};
    currentPageQuestions.forEach((question: Question) => {
      // Handle default email validation (usually on first page)
      if (question.default === 'true') {
        schema[`question_${question.id}`] = Yup.string()
          .email('Invalid email format')
          .required('Email is required');
      }
      // Handle other required fields
      else if (question.required) {
        if (question.type === 'Multiple choice') {
          schema[`question_${question.id}`] = Yup.array()
            .min(1, 'At least one option must be selected')
            .required('This field is required');
        } else if (question.type === 'Date') {
          schema[`question_${question.id}`] = Yup.date()
            .typeError('Invalid date format')
            .required('This field is required');
        } else if (question.type === 'Number') {
          schema[`question_${question.id}`] = Yup.number()
            .typeError('Must be a number')
            .required('This field is required');
        } else {
          schema[`question_${question.id}`] = Yup.string()
            .trim()
            .required('This field is required');
        }
      }
    });
    return Yup.object().shape(schema);
  }, [currentPageQuestions]);

  // Auto-save form data whenever values change - but only after initialization
  useEffect(() => {
    if (!isInitialized) return;

    const saveCurrentFormData = () => {
      // Don't save if allFormData is empty (after clearing cache)
      if (Object.keys(allFormData).length === 0) return;

      const cache: FormCache = {
        allFormData,
        currentPage,
        submissionId,
        submittedPages: Array.from(submittedPages),
        clientData,
        lastSaved: new Date().toISOString(),
      };
      saveFormCache(formDetails.id, cache);
    };

    const timeoutId = setTimeout(saveCurrentFormData, 1000); // Debounce saving
    return () => clearTimeout(timeoutId);
  }, [
    allFormData,
    currentPage,
    submissionId,
    submittedPages,
    clientData,
    formDetails.id,
    isInitialized,
  ]);

  const determineEvent = (values: any) => {
    if (values?.event == 'OTHER') {
      return values?.other;
    }
    return values?.event;
  };

  const handleClientActivity = async (
    data: any,
    answerDetails: any,
    method: 'update' | 'insert'
  ) => {
    console.log('data', data);
    console.log('answerDetails--333', answerDetails);
    if (data || method === 'update') {
      console.log('method', method);
      const client_id = data[0]?.client_id || data[0]?.id;
      const activity_payload = {
        client_id: client_id,
        activity_type: 'form_submitted',
        activity_date: new Date().toISOString(),
        details: {
          form_id: formDetails?.id,
          form_name: formDetails?.title,
          answer_details: answerDetails,
          page: currentPage,
        },
        organization_id: formDetails?.organization_id || data?.organization_id,
      };

      console.log('acativity_payload', activity_payload);
      console.log('data', data);

      if (method === 'insert') {
        // For first page submission - INSERT new activity

        const activityUploadData =
          await createClientActivities(activity_payload);
        // const { error: activityUploadError, data: activityUploadData } =
        //   await supabase
        //     .from(tableNames.client_activities)
        //     .insert(activity_payload)
        //     .select();

        console.log('res', activityUploadData);

        if (!activityUploadData) {
          throw new Error('Error creating client activity');
        }

        // Store the activity ID for future updates
        if (activityUploadData && activityUploadData.length > 0) {
          localStorage.setItem(
            'activityId',
            activityUploadData[0]?.id?.toString()
          );
        }
      } else if (method === 'update') {
        console.log('update flow');

        const activityId = localStorage.getItem('activityId');
        console.log('activityId', activityId);
        // For subsequent pages - UPDATE existing activity using stored ID

        if (activityId) {
          const updateActivityPayload = {
            activity_date: new Date().toISOString(),
            details: {
              form_id: formDetails?.id,
              form_name: formDetails?.title,
              answer_details: answerDetails[0]?.answer_details,
              page: currentPage,
            },
          };

          try {
            const updateData = await updateClientActivities({
              activityId: Number(activityId),
              updateData: updateActivityPayload,
            });

            console.log('updateData', updateData);
          } catch (error) {
            console.error('Error updating activity:', error);
            // Handle error appropriately
          }
        }
      }
    }
  };

  const handleNotableDates = async (values: any, clientData: any) => {
    const notableDateQuestions = currentPageQuestions
      ?.map((question: Question) => {
        if (question?.type === 'Date' && question?.notableDate) {
          return {
            ...question,
            answer: values[`question_${question.id}`],
          };
        }
        return null;
      })
      .filter(Boolean);

    if (notableDateQuestions?.length && clientData && clientData.length > 0) {
      const promises = notableDateQuestions?.map((question: any) => {
        return createNotableDateAsync({
          event: determineEvent({
            event: question?.event,
            other: question?.other,
          }),
          date: question?.answer,
          client_id: clientData[0]?.client_id || clientData[0]?.id,
          organization_id: clientData[0]?.organization_id,
        });
      });
      await Promise.all(promises);
    }
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    validateOnBlur: true,
    validateOnChange: true,
    enableReinitialize: false, // Disable automatic reinitialization to prevent flickering

    onSubmit: async (values) => {
      try {
        setIsLoading(true);

        console.log('values', values);

        // Update allFormData with current page values
        const updatedAllFormData = { ...allFormData, ...values };
        setAllFormData(updatedAllFormData);

        // Use persistent clientData or fetch on first page
        let currentClientData = clientData;

        // Only fetch client data on first page
        if (currentPage === 1) {
          // Find the email question (where default is "true") - typically on page 1
          const emailQuestion = formDetails.questions.find(
            (q: Question) => q.default === 'true'
          );

          let email = '';
          if (emailQuestion && emailQuestion.page === currentPage) {
            email = values[`question_${emailQuestion.id}`];
          }

          // Find existing client only if we have an email (usually page 1)
          if (email) {
            const response = await fetch(
              `/api/public/form-answers/email/${email}?organization_id=${formDetails?.organization_id}`,
              {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
              }
            );

            if (response.ok) {
              currentClientData = await response.json();
              setClientData(currentClientData); // Persist client data
            }
          }

          console.log('currentClientData', currentClientData);

          // create new client if not found
          if (currentClientData?.length === 0) {
            const insert = {
              email: email?.toLowerCase() || null,
              organization_id: formDetails?.organization_id,
              active_slp: formDetails?.user_id,
              display_name: email?.split('@')[0] || '',
            };

            const newClientCreatedData = await createClient(insert);

            console.log('data----555', newClientCreatedData);

            if (newClientCreatedData) {
              // create client emails
              const insertClientEmail = {
                client_id: newClientCreatedData[0]?.id,
                email: newClientCreatedData[0]?.email,
                organization_id: newClientCreatedData[0]?.organization_id,
              };
              const createdClientEmail =
                await createClientEmails(insertClientEmail);

              if (!createdClientEmail) {
                toaster.create({
                  description: 'Something went wrong.',
                  type: 'error',
                });
                throw Error('Error creating client email');
              }
              // create client activity
              const activitiesPayload = {
                client_id: newClientCreatedData[0]?.id,
                activity_type: 'client_created',
                activity_date: new Date().toISOString(),
                details: {
                  created_by: 'manual',
                },
                organization_id: newClientCreatedData[0]?.organization_id,
              };
              const createActivityData =
                await createClientActivities(activitiesPayload);
              if (!createActivityData) {
                toaster.create({
                  description: 'Something went wrong.',
                  type: 'error',
                });
                throw Error('Error creating client activity');
              }
            }

            currentClientData = newClientCreatedData;
            setClientData(currentClientData); // Persist client data
          }
        }

        // console.log('clientData------444', currentClientData);
        // console.log('curentPage', currentPage);

        // Create answer details for current page questions only
        const answer_details =
          currentPageQuestions?.map((question: Question) => ({
            id: question.id,
            qt: question.qt,
            show_answers: 'true',
            page: question.page,
            ans:
              question.type === 'Multiple choice'
                ? values[`question_${question.id}`]
                : values[`question_${question.id}`] || '',
          })) || [];

        // First page submission (INSERT)
        if (currentPage === 1) {
          console.log('clientData------444', currentClientData);
          const insertData = {
            form_id: formDetails?.id,
            client_id:
              currentClientData[0]?.client_id ||
              currentClientData[0]?.id ||
              null,
            form_title: formDetails?.title,
            organization_id: formDetails?.organization_id,
            answer_details,
            first_name: currentClientData[0]?.client?.first_name || '',
            last_name: currentClientData[0]?.client?.last_name || '',
            email: currentClientData[0]?.email || '',
            current_page: currentPage,
            is_complete: false,
          };

          const result = await createAnswers(insertData);
          // console.log('result', result);
          setSubmissionId(result[0]?.id); // Store the submission ID for future updates

          // Handle client activity
          await handleClientActivity(
            currentClientData,
            answer_details,
            'insert'
          );
        }
        // Subsequent pages (UPDATE)
        else {
          if (!submissionId) {
            throw new Error(
              'No submission ID found. Please start from page 1.'
            );
          }

          const updateData = {
            id: submissionId,
            answer_details, // This will be merged with existing answers
            current_page: currentPage,
            is_complete: currentPage === Math.max(...pages), // Mark complete on last page
          };

          const updatedRes = await updateAnswers(updateData);

          console.log('updatedRes', updatedRes);

          // Handle client activity
          await handleClientActivity(currentClientData, updatedRes, 'update');
        }

        // Handle notable dates
        await handleNotableDates(values, currentClientData);

        // Mark current page as submitted
        setSubmittedPages((prev) => new Set([...prev, currentPage]));

        // Show success message
        toaster.create({
          description: `Page ${currentPage} submitted successfully`,
          type: 'success',
        });

        // Navigate to next page or success page
        if (currentPage < Math.max(...pages)) {
          setCurrentPage(currentPage + 1);
          // DON'T RESET FORM - Keep cached data for navigation
        } else {
          setDisableButton(true); // Disable button on last page
          // Clear cache on successful completion
          clearFormCache(formDetails.id);
          setHasCachedDataFromStorage(false);
          // Final page submitted, redirect to success page
          router.push(
            `/form/${formDetails?.organization_name}/${formDetails?.slug}/success`
          );
        }
      } catch (error) {
        console.error('Error submitting form page:', error);
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
      } finally {
        setIsLoading(false);
      }
    },
  });

  // Update allFormData whenever formik values change (for auto-save) - but only after initialization
  useEffect(() => {
    if (isInitialized) {
      setAllFormData((prev) => ({ ...prev, ...formik.values }));
    }
  }, [formik.values, isInitialized]);

  // Manually reinitialize form when page changes - PRESERVE CACHED DATA
  useEffect(() => {
    if (isInitialized) {
      // Don't reset the form - just update with current page values from cache
      const currentPageValues: Record<string, any> = {};
      currentPageQuestions.forEach((question) => {
        const fieldName = `question_${question.id}`;
        const cachedValue = allFormData[fieldName];
        currentPageValues[fieldName] =
          cachedValue !== undefined
            ? cachedValue
            : question.type === 'Multiple choice'
              ? []
              : '';
      });

      formik.setValues(currentPageValues, false); // false prevents validation
    }
  }, [currentPage, isInitialized]); // Don't include currentPageQuestions to avoid infinite loop

  // console.log('submissionId', submissionId);

  const goToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= Math.max(...pages)) {
      // Save current page data before navigating
      setAllFormData((prev) => ({ ...prev, ...formik.values }));
      setCurrentPage(pageNumber);
      // Don't reset form here, let the effect handle it
    }
  };

  const goToNextPage = () => {
    if (currentPage < Math.max(...pages)) {
      // Save current page data before navigating
      setAllFormData((prev) => ({ ...prev, ...formik.values }));
      setCurrentPage(currentPage + 1);
      // Don't reset form here, let the effect handle it
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      // Save current page data before navigating
      setAllFormData((prev) => ({ ...prev, ...formik.values }));
      setCurrentPage(currentPage - 1);
      // Don't reset form here, let the effect handle it
    }
  };

  const clearCache = () => {
    // Clear localStorage first
    clearFormCache(formDetails.id);

    // Reset all state
    setAllFormData({});
    setCurrentPage(1);
    setSubmissionId(null);
    setSubmittedPages(new Set());
    setClientData(null);
    setHasCachedDataFromStorage(false);

    // Force complete form recreation
    setFormKey((prev) => prev + 1);

    // Also reset formik values immediately
    setTimeout(() => {
      formik.resetForm({
        values: currentPageQuestions.reduce((acc, question) => {
          acc[`question_${question.id}`] =
            question.type === 'Multiple choice' ? [] : '';
          return acc;
        }, {} as any),
      });
    }, 0);

    toaster.create({
      description: 'Form data cleared successfully',
      type: 'success',
    });
  };

  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === Math.max(...pages);
  const isPageSubmitted = submittedPages.has(currentPage);

  return {
    formik,
    isLoading,
    currentPage,
    pages,
    currentPageQuestions,
    isFirstPage,
    isLastPage,
    isPageSubmitted,
    submissionId,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    disableButton,
    clearCache, // New function to manually clear cache
    hasCachedData: hasCachedDataFromStorage, // Use the actual cached data flag instead
    isInitialized, // Export initialization state
    formKey, // Export form key for forcing recreation
  };
};

export default useMultiPageFormHook;
