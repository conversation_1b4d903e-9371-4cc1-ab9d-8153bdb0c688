export const tableNames = {
  bookings: 'bookings',
  products: 'products',
  package_products: 'package_products',
  client_emails: 'client_emails',
  client_activities: 'client_activities',
  clients: 'clients',
  linked_clients: 'linked_clients',
  followups: 'followups',
  invoices: 'invoices',
  packages: 'packages',
  pay_definitions: 'pay_definitions',
  pay_schedules: 'pay_schedules',
  slp_notes: 'slp_notes',
  slps: 'slps',
  form_answers: 'form_answers',
  users: 'users',
  forms: 'forms',
  user_permissions: 'user_permissions',
  permissions: 'permissions',
  referrals: 'referrals',
  refunds: 'refunds',
  email_activities: 'email_activities',
  organizations: 'organizations',
  transactions: 'transactions',
  events: 'events',
  availabilities: 'availabilities',
  template: 'template',
  patch_notes: 'patch_notes',
  subscriptions: 'subscriptions',
  subscription_plans: 'subscription_plans',
  subscription_invoices: 'subscription_invoices',
  emails: 'emails',
  notes: 'notes',
  todo: 'todos',
  supports: 'supports',
  onboarding: 'onboarding',
  tags: 'tags',
  package_list: 'package_list',
  stripe_unprocessed_data: 'stripe_unprocessed_data',
  user_documents: 'user_documents',
  client_documents: 'client_documents',
  package_type: 'package_type',
  client_package_products: 'client_package_products',
  services: 'services',
  redeemed_sessions: 'redeemed_sessions',
  purchased_package_items: 'purchased_package_items',
  purchases: 'purchases',
  package_offering: 'package_offering',
  package_items: 'package_items',
  payment_method_clients: 'payment_method_clients',
  notable_dates: 'notable_dates',
  invoice_items: 'invoice_items',
  taxes: 'taxes',
  services_purchases: 'services_purchases',
  klaviyo_organization: 'klaviyo_organization',
  klaviyo_trackable_events: 'klaviyo_trackable_events',
};
