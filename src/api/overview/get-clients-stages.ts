import { query<PERSON>ey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

type Params = {
  since_date: string;
};

interface RawConsultantData {
  consultant: string;
  total: number;
  stages: Record<string, { count: number; percentage: number }>;
}

export interface ConsultantData {
  consultant: string;
  stages: Record<string, number>;
  total: number;
}

async function getClientStages(params: Params): Promise<ConsultantData[]> {
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  const url = new URL('/api/overview/client-stages', window.location.origin);
  url.searchParams.set('since_date', params.since_date);

  if (!org?.id) {
    throw new Error('Missing organization ID');
  }

  url.searchParams.set('org_id', org?.id.toString());

  const response = await fetch(url.toString(), {
    method: 'GET',
  });

  const json = await response.json();

  if (!response.ok) {
    throw new Error(json.error || 'Failed to fetch client stages');
  }

  if (!Array.isArray(json)) {
    throw new Error('Expected an array of consultant data');
  }

  return json.map((item: RawConsultantData) => ({
    consultant: item.consultant,
    total: item.total,
    stages: Object.fromEntries(
      Object.entries(item.stages).map(([stage, data]) => [stage, data.count])
    ),
  }));
}

type QueryFnType = typeof getClientStages;

export const useGetClientStagesQuery = (
  params: Params,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    queryKey: [queryKey.overview.clientStages, params],
    queryFn: () => getClientStages(params),
    ...config,
  });
};
