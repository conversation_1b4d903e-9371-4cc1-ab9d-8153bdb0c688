'use client';
import { Button } from '@/components/ui/button';
// import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { Flex, Heading, Image, Text, VStack } from '@chakra-ui/react';

export default function IntegrationCard({
  service,
  description,
  icon,
  connected,
  onConnectClick,
  onDisconnectClick,
  width = '40rem',
  onEditClick,
}: any) {
  const handleClick = () => {
    if (connected) {
      onDisconnectClick();
    } else {
      onConnectClick();
    }
  };

  return (
    <Flex
      w={width}
      p={4}
      justifyContent="space-between"
      bg={'gray.50'}
      rounded={'.5rem'}
    >
      <Flex gap={'1rem'}>
        <Image w={'3rem'} src={icon?.src} alt={service} />
        <VStack maxW={'25rem'} align="start" gap={1}>
          <Heading as="h3" size="md">
            {service}
          </Heading>
          <Text fontSize="sm" color="gray.600">
            {description}
          </Text>
        </VStack>
      </Flex>
      <Flex alignItems={'center'} gap={'1.25rem'}>
        {onEditClick && (
          <Button variant={'ghost'} onClick={onEditClick}>
            Edit
          </Button>
        )}
        <Button
          onClick={handleClick}
          colorScheme={connected ? 'green' : 'blue'}
          bg={connected ? 'transparent' : 'white'}
          color={connected ? 'red' : 'black'}
          fontWeight={'semibold'}
        >
          {connected ? 'Disconnect' : 'Connect'}
        </Button>
      </Flex>
    </Flex>
  );
}
