export const queryKey = {
  client: {
    getById: 'get-client-by-id',
    search: 'search-clients',
    searchAll: 'search-clients-all',
    getWaitlist: 'get-all-waitlist',
    getActivities: 'get-all-activities',
    updateClient: 'update-client',
    import: 'import-clients',
    getByEmail: 'get-client-by-email',
    getInvoices: 'get-client-invoices',
    getCardDetails: 'get-card-details',
    getClientDocuments: 'get-clients-document',
    newSearch: 'new-search-client',
  },

  bookings: {
    getAllBookings: 'get-all-bookings',
    getByClient: 'get-by-client',
    updateBooking: 'update-bookings',
    createBooking: 'create-bookings',
    getById: 'get-booking-by-id',
    getLinkable: 'get-linkable-bookings',
    linkBooking: 'link-booking',
    getSlpSessions: 'get-slp-sessions',
    getSlpBookings: 'get-slp-bookings',
    getSlpCalenderBookings: 'get-slp-calender-bookings',
    getSlots: 'get-all-slots',
  },
  events: { create: 'create-event' },
  consultations: {
    getAllConsultations: 'get-all-consultations',
  },
  followUp: {
    getAllFollowup: 'get-all-follow-up',
    updateFollowUp: 'update-follow-up',
    addNewFollowup: 'add-new-follow-up',
  },
  invoices: {
    getByUser: 'get-invoices-by-user',
    getUnlinkedSlpInvoices: 'get-unlinked-slp-invoices',
    getDoneInvoices: 'get-done-invoices',
    getByClients: 'get-invoices-by-client',
    getPending: 'get-pending-invoices',
    getNotPaid: 'get-not-paid-invoives',
    update: 'update-invoices',
    delete: 'delete-invoices',
    getAllRaw: 'get-al-invoices-raw',
    createAndSendInvoice: 'create-and-send-invoice',
    summary: 'get-invoices-summary',
  },
  linkedClients: {
    getByClientId: 'get-linked-client-by-client-id',
    getParentClient: 'get-parent-client',
  },
  products: {
    getAllProducts: 'get-all-products',
    import: 'import-products',
  },
  forms: {
    getAllForms: 'get-all-forms',
    getFormById: 'get-form-by-id',
    getFormAnswers: 'get-form-answers',
    updateFormAnswers: 'update-form-answers',
    updateClientActivities: 'update-client-activities',
    getFormAnswersById: 'get-form-answers-by-id',
    uploadFormAnswers: 'upload-form-answers',
    createClient: 'create-client-from-form',
    createClientActivities: 'create-client-activities-from-form',
    createClientEmails: 'create-client-emails-from-form',
  },
  overview: {
    getStats: 'get-overview-stats',
    aveargeLtv: 'get-average-ltv',
    adminaveargeLtv: 'get-admin-average-ltv',
    invoiceStats: 'get-invoice-stats',
    stageStats: 'get-stage-stats',
    consultationStats: 'get-consultation-stats',
    topStats: 'get-top-stats-overview',
    referralCount: 'get-referral-count',
    goalsCount: 'get-goals-count',
    clientStages: 'get-client-stages',
    leadPipelineClients: 'lead-pipeline-clients',
  },
  users: {
    getAllSlp: 'get-all-slp',
    getAllUsers: 'get-all-users',
    getById: 'get-user-by-id',
    getByEmail: 'get-user-by-email',
    getBySlug: 'get-user-by-slug',
    getSlpIdView: 'get-slp-id-view',
    getSlpClientsCount: 'get-slp-clients-count',
    getSlpDocuments: 'get-slp-clients-document',
    getNonInvoicedClients: 'get-non-invoiced-clients',
  },
  slpNotes: {
    updateSlpNote: 'update-slp-note',
    getByClientId: 'get-slp-note-by-client-id',
    createSlpNote: 'create-slp-note',
    getAllSlpNotes: 'get-all-slp-notes',
  },
  slp: {
    getStatistics: 'get-slp-statistics',
  },
  paySchedule: {
    getBySlpId: 'get-pay-schedule-by-slp-id',
  },
  refunds: {
    getAll: 'get-all-refunds',
  },
  packages: {
    getByClient: 'get-by-client-id',
    updatePackage: 'update-package',
    purchasePackage: 'purchase-package',
    getAllPurchasedPackage: 'get-all-purchased-package',
    getAllPackages: 'get-all-packages-raw',
    getPackageOfferings: 'get-package-offerings',
    updatePurchasedPackageItem: 'update-purchased-package-item',
  },
  redeemSession: {
    deleteRedeemedSession: 'delete-redeemed-session',
    updateRedeemedSession: 'update-redeemed-session',
  },
  package_list: {
    getByClient: 'get-by-client-id',
    updatePackageList: 'update-package-list',
    createPackageList: 'create-package-list',
    getAllPackageList: 'get-all-package-list',
  },
  referrals: {
    getAllReferrals: 'get-all-referrals',
    getAllUnlinkedInvoices: 'get-all-unlinked-invoices',
    getRefereeById: 'get-referee-by-id',
  },
  emails: {
    readEmails: 'read-email',
    readDBEmails: 'read-DB-email',
  },
  todos: {
    getAll: 'get-all-todos',
  },
  tags: {
    getAll: 'get-all-tags',
  },
  support: {
    getAll: 'get-all-support',
  },
  templates: {
    getAll: 'get-all-templates',
    create: 'create-templates',
    update: 'update-templates',
    delete: 'delete-templates',
  },
  subscription: {
    get: 'get-organization-subscription',
    getPlans: 'get-organization-subscription-palns',
    getinvoices: 'get-organization-subscription-invoices',
  },
  notes: {
    getAll: 'get-all-notes',
    create: 'create-notes',
    update: 'update-notes',
    delete: 'delete-notes',
    getNoteByBookingId: 'get-notes-by-booking-id',
  },
  organizations: {
    getAll: 'get-organization-all',
    getBySlug: 'get-organization-by-slug',
  },
  transactions: {
    getAll: 'get-all-transactions',
    getByClientId: 'get-all-client-transactions',
  },
  services: {
    getAll: 'get-all-services',
  },
  redeemedSession: {
    getAll: 'get-all-services',
  },
  notableDates: {
    getAll: 'get-all-notable-dates',
    create: 'create-notable-dates',
    update: 'update-notable-dates',
    delete: 'delete-notable-dates',
  },
  newSf: {
    getAllPurchases: 'newsf-get-al-purchases',
    getAllInvoices: 'newsf-get-all-invoices',
  },
  trackableEvents: {
    getAll: 'get-all-trackable-events',
  },
};
