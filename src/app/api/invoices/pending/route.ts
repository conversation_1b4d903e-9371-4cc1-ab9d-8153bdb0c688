import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
// import supabase from '@/lib/supabase/client';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const supabase = createSupabaseServer();

    const { searchParams } = new URL(request.url);
    const linkedSlp = searchParams.get('linkedSlp') || '';
    const orgId = searchParams.get('org') || '';

    const parsedOrg = parseInt(orgId);

    let query = supabase
      .from(tableNames.slp_notes)
      .select(
        `*, 
            users (*), 
            invoices:invoices!slp_notes_invoice_id_fkey(*), 
    clients:clients!slp_notes_client_id_fkey(*, client_emails(*)), 
            bookings(*)`
      )
      .eq('status', 'Pending')
      .eq('organization_id', parsedOrg)
      .order('slp_id');

    if (linkedSlp === 'false') {
      query = query.is('invoice_id', null);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error?.message);
    }

    return NextResponse.json({ success: true, data });
  } catch (error: any) {
    return NextResponse.json(
      { success: false, message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}
