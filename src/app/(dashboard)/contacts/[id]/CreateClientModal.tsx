import { CustomModal } from '@/components/elements/modal/custom-modal';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';

import {
  Box,
  Flex,
  Grid,
  GridItem,
  Stack,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useAddNewClientHook } from '@/hooks/clients/useAddNewClientHook';
import React from 'react';
import { Button } from '@/components/ui/button';
import { Field } from '@/components/ui/field';
import { useContactStages } from '@/hooks/clients/useGetContactsStages';
import CTABtn from '@/components/elements/CTABtn';

export default function CreateClientModal({ id }: { id?: number }) {
  const { open, onClose, onOpen } = useDisclosure();
  const { contactStagesOptions } = useContactStages(false);
  const {
    values,
    touched,
    handleFormSubmit,
    errors,
    handleChange,
    setFieldValue,
    handleFirstNameChange,
    handleLastNameChange,
    handleDisplayNameChange,
    loading,
    stateOptions,
    countryOptions,
    cityOptions,
  } = useAddNewClientHook({ onClose, id });

  return (
    <div>
      <CTABtn buttonName="New Client" variant={2} onClick={onOpen} />

      <CustomModal
        w={{ base: '90%', md: '40rem' }}
        open={open}
        onOpenChange={onClose}
      >
        <Box my={'1rem'}>
          <Text fontSize={'1.2rem'} textAlign={'center'} fontWeight={'bold'}>
            Create a new client
          </Text>
        </Box>

        <form onSubmit={handleFormSubmit}>
          <Stack gap={'1rem'} pt={'1rem'}>
            {/* First Name (Required) */}

            <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap="1rem">
              <GridItem>
                <StringInput
                  fieldProps={{
                    invalid: touched.first_name && !!errors.first_name,
                    label: 'First Name',
                    required: true,
                    errorText: errors.first_name,
                  }}
                  inputProps={{
                    name: 'first_name',
                    value: values.first_name?.trim() || undefined,
                    onChange: handleFirstNameChange,
                  }}
                />
              </GridItem>

              <GridItem>
                <StringInput
                  inputProps={{
                    name: 'last_name',
                    value: values.last_name?.trim() || undefined,
                    onChange: handleLastNameChange,
                  }}
                  fieldProps={{
                    invalid: touched.last_name && !!errors.last_name,
                    label: 'Last Name',
                    errorText: errors.last_name,
                    required: true,
                  }}
                />
              </GridItem>
            </Grid>
            {/* <StringInput
              fieldProps={{
                invalid: touched.first_name && !!errors.first_name,
                label: 'First Name',
                required: true,
                errorText: errors.first_name,
              }}
              inputProps={{
                name: 'first_name',
                value: values.first_name?.trim() || undefined,
                onChange: handleFirstNameChange,
              }}
            /> */}

            {/* Middle Name (Optional) */}

            {/* <StringInput
              inputProps={{
                name: 'middle_name',
                value: values.middle_name || undefined,
                onChange: handleChange,
              }}
              fieldProps={{
                invalid: touched.middle_name && !!errors.middle_name,
                label: 'Middle Name',
                errorText: errors.middle_name,
              }}
            /> */}

            {/* Last Name (Required) */}

            {/* <StringInput
              inputProps={{
                name: 'last_name',
                value: values.last_name?.trim() || undefined,
                onChange: handleLastNameChange,
              }}
              fieldProps={{
                invalid: touched.last_name && !!errors.last_name,
                label: 'Last name',
                errorText: errors.last_name,
                required: true,
              }}
            /> */}

            {/* Display Name (Required) */}

            <StringInput
              inputProps={{
                name: 'display_name',
                value: values.display_name || '',
                onChange: handleDisplayNameChange,
              }}
              fieldProps={{
                invalid: touched.display_name && !!errors.display_name,
                label: 'Customer Display Name',
                errorText: errors.display_name,
                required: true,
              }}
            />

            {/* Email (Optional) */}

            <StringInput
              inputProps={{
                name: 'email',
                type: 'email',
                value: values.email || undefined,
                onChange: handleChange,
              }}
              fieldProps={{
                invalid: touched.email && !!errors.email,
                label: 'Email',
                errorText: errors.email,
                required: true,
              }}
            />

            {/* Phone (Optional) */}

            <StringInput
              inputProps={{
                name: 'phone',
                value: values.phone || undefined,
                onChange: handleChange,
              }}
              fieldProps={{
                invalid: touched.phone && !!errors.phone,
                label: 'Phone',
                errorText: errors.phone,
              }}
            />

            {/* Province (Required) */}

            {/* <Field
              label={'Province'}
              errorText={errors.province}
              invalid={touched.province && !!errors.province}
              // required={true}
            >
              <CustomSelect
                placeholder="Select province"
                options={provinceOptions}
                onChange={(val) => setFieldValue('province', val.value)}
                defaultValue={provinceOptions.find(
                  (item) =>
                    item.value.toLowerCase() === values.province.toLowerCase()
                )}
              />
            </Field> */}

            {/* country */}
            <CustomSelect
              onChange={(option) => {
                setFieldValue('country', option.value);
              }}
              selectedOption={countryOptions?.find(
                (option) => option?.value?.isoCode == values?.country?.isoCode
              )}
              options={countryOptions}
              label="Country"
            />
            <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap="1rem">
              <GridItem>
                {/* state */}
                <CustomSelect
                  onChange={(option) => {
                    setFieldValue('state', option.value);
                  }}
                  selectedOption={stateOptions?.find(
                    (option) => option?.value?.isoCode == values?.state?.isoCode
                  )}
                  options={stateOptions}
                  label="State/Province"
                />
              </GridItem>

              <GridItem>
                {/* city */}
                <CustomSelect
                  onChange={(option) => {
                    setFieldValue('city', option.value);
                  }}
                  selectedOption={cityOptions?.find(
                    (option) => option?.value?.name == values?.city?.name
                  )}
                  options={cityOptions}
                  label="City"
                />
              </GridItem>
            </Grid>

            {/* Status (Required) */}

            <Field
              label={'Status'}
              errorText={errors.stage}
              invalid={touched.stage && !!errors.stage}
              required={true}
            >
              <CustomSelect
                placeholder="Select status"
                options={contactStagesOptions}
                onChange={(val) => setFieldValue('stage', val.value)}
                defaultValue={contactStagesOptions?.find(
                  (item: any) =>
                    item.value.toLowerCase() === values.stage.toLowerCase()
                )}
              />
            </Field>

            {/* Buttons */}
            <Flex
              my={'1.8rem'}
              alignItems={'center'}
              justifyContent={'space-between'}
            >
              <Button
                onClick={onClose}
                variant={'outline'}
                minH={'3rem'}
                minW={'15rem'}
              >
                Cancel
              </Button>
              <Button
                loading={loading}
                minH={'3rem'}
                bg={'primary.500'}
                minW={'15rem'}
                type="submit"
              >
                Save
              </Button>
            </Flex>
          </Stack>
        </form>
      </CustomModal>
    </div>
  );
}
