//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getReferralCount(data: any) {
  const response = await fetch(`/api/overview/referral-count`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
  const json = await response.json();
  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch stats');
  }
  return json;
}

type QueryFnType = typeof getReferralCount;

export const useGetReferralCountQuery = (
  data: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry: false,
    queryKey: [queryKey.overview.referralCount, data],
    queryFn: () => getReferralCount(data),
    ...config,
  });
};
