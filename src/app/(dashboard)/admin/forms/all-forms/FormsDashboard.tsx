import AnimateLoader from '@/components/elements/loader/animate-loader';
import {
  Box,
  Center,
  Flex,
  GridItem,
  Heading,
  Link,
  SimpleGrid,
  Skeleton,
  // Spinner,
  Stack,
  Tabs,
  Text,
} from '@chakra-ui/react';

// import { CustomModal } from '@/components/elements/modal/custom-modal';
import CTABtn from '@/components/elements/CTABtn';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import moment from 'moment';
import FormCard from '../_components/FormCard';
import SubmittedAnswers from './SubmittedAnswers';

const FormsDashboard = ({
  AllAnswers,
  getFormsViewState,
  AllAnswersLoading,
  GetAllFormsLoading,
  GetAllFormsData,
  loading,
  organizationSlug,
  handleDeleteForm,
  openDelete,
  onCloseDelete,
  formIdToBeDel,
  handleConfirmDeleteFormModal,
}: any) => {
  // console.log('AllAnswers', AllAnswers);
  return (
    <>
      <Box
        display={'flex'}
        flexDirection={'column'}
        alignItems={'center'}
        width={{ base: '95%', lg: '80%' }}
        mx={'auto'}
        pb={'20'}
      >
        {/* header */}

        <Box
          display={'flex'}
          alignItems={'center'}
          justifyContent={'space-between'}
          width={'100%'}
        >
          <Box>
            <Heading
              fontSize={{ base: '1.3rem', md: '2rem' }}
              fontWeight={'semibold'}
              mb={{ base: '1', lg: '3' }}
            >
              Forms
            </Heading>
            <Text
              fontSize={{ base: 'sm', md: 'md' }}
              color={'gray.300'}
              fontWeight={'500'}
            >
              Manage your form templates and submissions
            </Text>
          </Box>

          <Box display={'flex'} alignItems={'center'} gap={'1rem'}>
            {/* <Tooltip content="Learn more">
              <Center
                h={{ base: '30px', md: '40px' }}
                _hover={{ bg: '#d96847' }}
                w={{ base: '30px', md: '40px' }}
                bg={'primary.500'}
                rounded={'full'}
                onClick={handleOpenLearnModal}
              >
                <TbMessageQuestion size="15px" color="#fff" />
              </Center>
            </Tooltip> */}

            <Link href={'/forms/templates'} className="btnLink">
              <Center
                border="1px solid"
                borderColor={'gray.50'}
                rounded={'md'}
                py={'2.5'}
                px={'3.5'}
                gap={'2.5'}
                cursor={'pointer'}
                _hover={{ backgroundColor: 'gray.50', borderColor: 'gray.100' }}
              >
                <Text fontSize={'md'} fontWeight={'500'}>
                  Browse Templates
                </Text>
              </Center>
            </Link>
            <CTABtn
              buttonName="Create Form"
              variant={2}
              onClick={() => getFormsViewState('create')}
            />
          </Box>
        </Box>

        {/* header ends */}

        {/* body start */}
        <Tabs.Root defaultValue="templates" w={'full'} lazyMount mt={'5'}>
          <Tabs.List
            position={'sticky'}
            top={'20'}
            zIndex={'10'}
            bg={'white'}
            borderBottom={{ lg: '1px solid' }}
            borderTop={{ lg: '1px solid' }}
            borderColor={{ lg: 'gray.50' }}
            overflow={'hidden'}
          >
            <Tabs.Trigger
              value="templates"
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Templates
            </Tabs.Trigger>
            <Tabs.Trigger
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
              value="response"
            >
              Client Responses
            </Tabs.Trigger>
          </Tabs.List>
          {/**Template content */}
          <Tabs.Content value="templates" width={'full'} px={'4'}>
            {GetAllFormsLoading ? (
              <Center h={'20rem'}>
                <AnimateLoader />
              </Center>
            ) : (
              <>
                <Box
                  display={'flex'}
                  width={'100%'}
                  flexDirection={'column'}
                  alignItems={'start'}
                  mt={'4'}
                >
                  <Text fontSize={{ lg: 'lg' }} fontWeight={500}>
                    Template ({GetAllFormsData?.allForms?.length})
                  </Text>

                  {/* looped forms */}

                  <SimpleGrid
                    gap={'1rem'}
                    mt={'3'}
                    columns={{ base: 1, md: 3 }}
                    width={'100%'}
                  >
                    {GetAllFormsData?.allForms?.length > 0 ? (
                      GetAllFormsData?.allForms?.map((item: any) => {
                        return (
                          <GridItem key={item.id}>
                            {/* <Link
                              href={`forms/edit/${item?.id}`}
                              as={NextLink}
                              width={'100%'}
                            >
                            </Link> */}
                            <FormCard
                              handleConfirmDeleteFormModal={
                                handleConfirmDeleteFormModal
                              }
                              form={item}
                              organizationSlug={organizationSlug}
                              getFormsViewState={getFormsViewState}
                            />
                          </GridItem>
                        );
                      })
                    ) : (
                      <GridItem
                        colSpan={3}
                        justifyContent={'center'}
                        width={'100%'}
                      >
                        <Center>No Forms Created Yet</Center>
                      </GridItem>
                    )}
                  </SimpleGrid>

                  {/* looped forms end */}

                  {/* modal for confirm delete */}
                  {/* <CustomModal
              w={{ base: '15%', md: '20rem' }}
              open={openDelete}
              onOpenChange={onCloseDelete}
            >
              <Box
                p={4}
                borderRadius="md"
                textAlign={'center'}
                display={'flex'}
                flexDirection={'column'}
                justifyContent={'center'}
                alignItems={'center'}
                gap={'1rem'}
              >
                <Text mb={3} fontSize={'1.5rem'}>
                  Are you sure you want to delete this form?
                </Text>
                <Flex
                  gap={3}
                  width={'100%'}
                  mx={'auto'}
                  justifyContent={'center'}
                >
                  <Button
                    disabled={loading}
                    onClick={() => {
                      handleDeleteForm(formIdToBeDel);
                    }}
                    colorScheme="red"
                    width={'5rem'}
                  >
                    {loading ? <Spinner size={'sm'} /> : 'Yes'}
                  </Button>
                  <Button
                    onClick={onCloseDelete}
                    variant="outline"
                    width={'5rem'}
                  >
                    Cancel
                  </Button>
                </Flex>
              </Box>
            </CustomModal> */}
                  <ConsentDialog
                    handleSubmit={() => {
                      handleDeleteForm(formIdToBeDel);
                    }}
                    open={openDelete}
                    onOpenChange={onCloseDelete}
                    heading={'Confirm deletion?'}
                    note="This will permanently delete this Template."
                    isLoading={loading.delete}
                  />
                </Box>
              </>
            )}
          </Tabs.Content>
          <Tabs.Content value="response" width={'full'} px={'4'}>
            <Flex
              justifyContent={'flex-start'}
              alignItems={'center'}
              width={'100%'}
              mb={'4'}
            >
              <Text fontSize={{ lg: 'lg' }} fontWeight={500}>
                Clients Response
              </Text>
            </Flex>

            {AllAnswersLoading ? (
              // Skeletons while loading
              <Stack spaceY={3} width={'100%'}>
                <Skeleton
                  height="60px"
                  background="rgba(0, 0, 0, 0.1)"
                  opacity={0.7}
                  width="100%"
                  borderRadius="md"
                />
                <Skeleton
                  height="60px"
                  background="rgba(0, 0, 0, 0.1)"
                  opacity={0.7}
                  width="100%"
                  borderRadius="md"
                />
              </Stack>
            ) : (
              <>
                {AllAnswers.sort(
                  (a: any, b: any) =>
                    moment(b.created_at).valueOf() -
                    moment(a.created_at).valueOf()
                ).map((item: any, index: any) => (
                  <SubmittedAnswers
                    key={index}
                    item={item}
                    getFormsViewState={getFormsViewState}
                  />
                ))}
              </>
            )}
          </Tabs.Content>
        </Tabs.Root>
      </Box>
    </>
  );
};

export default FormsDashboard;
