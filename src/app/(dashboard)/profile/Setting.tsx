import { Stack, Heading, Text, Center } from '@chakra-ui/react';
import React from 'react';
import IntegrationCard from '../integrations/IntegrationCard';
import gmailLogo from '@/assets/gmail-logo.png';
import calendarLogo from '@/assets/gcalendar.png';
import { useIntegrations } from '../integrations/useIntegrations';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { TUseSlpHook } from '@/hooks/slp/useSlpHook';
import stripeLogo from '@/assets/stripe-logo.png';
import klaviyoLogo from '@/assets/klaviyo.png';
import KlaviyoEdit from './KlaviyoEdit';

export function Setting({ hook }: { hook: TUseSlpHook }) {
  const {
    handleGoogleDisconnect,
    gmailDisconnectDisclosure,
    isLoading,
    handleGoogleConnect,
    setGoogleService,
    googleService,
    userData,
    handleStripeConnect,
    stripeDisconnectDisclosure,
    handleStripeDisconnect,
    stripeLoading,
    handleKlaviyoConnect,
    klaviyoDisconnectDisclosure,
    handleKlaviyoDisconnect,
    disconnectLoading,
    editKlaviyoDisclosure,
  } = useIntegrations(hook.data);

  console.log('userData is ', userData);

  const { isFetching } = hook;

  if (isFetching)
    return (
      <Center h={'20rem'}>
        <AnimateLoader />
      </Center>
    );

  return (
    <Stack
      border={'1px solid #d1d5db'}
      rounded={'10px'}
      px={'4'}
      py={'6'}
      gap={'6'}
    >
      <Stack gap={'-2.5'}>
        <Heading>Application Preferences</Heading>
        <Text fontSize={'sm'} color={'#9ca3af'}>
          Customize your experience with the coaching platform
        </Text>
      </Stack>

      <IntegrationCard
        service="Gmail"
        description="Sync coaching emails with your gmail"
        width={'full'}
        icon={gmailLogo}
        user={hook.data}
        onConnectClick={() => handleGoogleConnect('gmail')}
        connected={userData?.is_gmail_connected}
        onDisconnectClick={() => {
          setGoogleService('gmail');
          gmailDisconnectDisclosure.onOpen();
        }}
      />

      <IntegrationCard
        service="Google Calendar"
        description="Sync coaching sessions with your calendar"
        width={'full'}
        icon={calendarLogo}
        user={hook.data}
        onConnectClick={() => handleGoogleConnect('calendar')}
        connected={userData?.is_google_calendar_connected}
        onDisconnectClick={() => {
          setGoogleService('calendar');
          gmailDisconnectDisclosure.onOpen();
        }}
      />

      <IntegrationCard
        service="Stripe"
        description="Manage your payments directly within our platform."
        icon={stripeLogo}
        user={userData}
        connected={userData?.organization?.stripe_user_id}
        onConnectClick={handleStripeConnect}
        onDisconnectClick={stripeDisconnectDisclosure.onOpen}
        width={'full'}
      />
      {userData?.organization?.owner === userData?.id && (
        <IntegrationCard
          service="Klaviyo"
          description="Manage your CRM and analytics directly within our platform."
          icon={klaviyoLogo}
          user={userData}
          connected={userData?.organization?.klaviyo_details?.refresh_token}
          onConnectClick={handleKlaviyoConnect}
          onDisconnectClick={klaviyoDisconnectDisclosure.onOpen}
          width={'full'}
          canEdit={userData?.organization?.klaviyo_details?.refresh_token}
          onEditClick={() => {
            if (userData?.organization?.klaviyo_details?.refresh_token) {
              editKlaviyoDisclosure.onOpen();
            }
          }}
        />
      )}

      <ConsentDialog
        open={gmailDisconnectDisclosure.open}
        onOpenChange={gmailDisconnectDisclosure.onClose}
        handleSubmit={handleGoogleDisconnect}
        note={`This action will completely remove your ${googleService === 'gmail' ? 'gmail' : 'calendar'} access integration.`}
        isLoading={isLoading}
        heading={'Are you sure?'}
        firstBtnText="Back"
        secondBtnText="Yes, Disconnect"
      />
      <ConsentDialog
        open={stripeDisconnectDisclosure.open}
        onOpenChange={stripeDisconnectDisclosure.onClose}
        handleSubmit={handleStripeDisconnect}
        note={`This action will completely remove your stripe integration.`}
        isLoading={stripeLoading}
        heading={'Are you sure?'}
        firstBtnText="Back"
        secondBtnText="Yes, Disconnect"
      />
      <ConsentDialog
        open={klaviyoDisconnectDisclosure.open}
        onOpenChange={klaviyoDisconnectDisclosure.onClose}
        handleSubmit={handleKlaviyoDisconnect}
        note={`This action will completely remove your klaviyo integration.`}
        isLoading={disconnectLoading}
        heading={'Are you sure?'}
        firstBtnText="Back"
        secondBtnText="Yes, Disconnect"
      />

      <KlaviyoEdit
        open={editKlaviyoDisclosure.open}
        setOpen={editKlaviyoDisclosure.onClose}
        user={userData}
      />
    </Stack>
  );
}
