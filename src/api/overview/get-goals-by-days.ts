import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

import { buildUrlWithQueryParams } from '@/utils/build-url-query';

// Define timezone as optional in the input type
interface GetGoalsForDaysData {
  days: string;
}

async function getOverviewGoalsForDays(data: GetGoalsForDaysData) {
  const baseUrl = '/api/overview/goals';
  const apiUrl = buildUrlWithQueryParams(baseUrl, data);
  const response = await fetch(apiUrl, { method: 'GET' });
  if (!response.ok) {
    throw new Error(`Error: ${response.status}`);
  }
  const json = await response.json();

  return json.data;
}

type QueryFnType = typeof getOverviewGoalsForDays;

export const useGetOverviewGoalsForSelectedDaysQuery = (
  data: GetGoalsForDaysData, // Updated type with optional timezone
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      if (failureCount < 1) return true;
      return false;
    },
    queryKey: ['get-overview-goals-days', data?.days],
    queryFn: () => getOverviewGoalsForDays(data), // Correct

    ...config,
  });
};
