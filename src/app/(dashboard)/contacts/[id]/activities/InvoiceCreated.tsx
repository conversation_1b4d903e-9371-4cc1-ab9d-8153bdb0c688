'use client';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import Status from '@/components/elements/status/Status';
import { getSessionColor } from '@/utils/color-helper';
import { Box, Center, Flex, Stack, Text } from '@chakra-ui/react';
import moment from 'moment';
import { FiCalendar, FiFileText, FiUser } from 'react-icons/fi';
import { GoDotFill } from 'react-icons/go';

export default function InvoiceCreated({
  slp,
  session_type,
  data,
  activity,
  handleSwitchTab,
}: any) {
  // Calculate the invoice total
  // const searchParams = useSearchParams();

  // const organizationId = searchParams.get('organization_id');
  const raw =
    typeof window != 'undefined' ? localStorage.getItem('UserState') : null;
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const invoiceTotal =
    org?.id === 1
      ? activity?.total_price
      : activity?.invoice_items?.reduce(
          (sum: number, item: any) => sum + item.price * item.quantity,
          0
        );
  // Format the invoice items text
  const invoiceItemsText =
    org?.id === 1
      ? activity?.product
      : activity?.invoice_items
          ?.map((item: any) => `${item.product_name} (${item.quantity})`)
          .join(', ');

  // console.log('data-formr', data);
  return (
    <Box w={'100%'} h={'fit'}>
      <Flex w={'full'} direction={'row'}>
        <Stack maxW={'3rem'} minW={'3rem'} gap={0} alignItems={'center'}>
          <GoDotFill color="#000" />
          <Box minH={'1rem'} bg={'#dee1e0'} h={'100%'} w={'.5px'}></Box>
        </Stack>
        <Flex
          w={'full'}
          maxW={'960px'}
          flexWrap={'wrap'}
          gap={'2'}
          direction={'column'}
          mb={'4'}
        >
          <Box>
            <Flex
              justifyContent={'space-between'}
              alignItems={'center'}
              gap={'1.5'}
            >
              <Text fontSize={'14px'} fontWeight={'500'}>
                Invoice Created
              </Text>
              {/* <Box fontSize={'sm'} textTransform={'capitalize'}>
                {activity?.status ? <Status name={activity?.status} /> : null}
              </Box> */}
            </Flex>
          </Box>
          <Box
            rounded={'4.8px'}
            border={'.0625rem solid #dee1e0'}
            py={'0.8rem'}
            px={'8px'}
            display={'flex'}
            //alignItems={'center'}
            w={'100%'}
            maxHeight={'fit'}
            minHeight={'3.5rem'}
            height={'3x.5rem'}
            gap={'1rem'}
            cursor={'pointer'}
            onClick={() => handleSwitchTab('invoices')}
            _hover={{
              boxShadow: 'lg',
              transition: 'all 0.2s ease-in-out',
            }}
          >
            <Box
              rounded={'4.8px'}
              fontSize={'16px'}
              display={'flex'}
              justifyContent={'center'}
              alignItems={'center'}
              minW={'36px'}
              w={'36px'}
              minH={'36px'}
              maxH={'36px'}
              maxW={'36px'}
              cursor={'pointer'}
              bg={'orange.50'}
              color={'orange.500'}
            >
              <FiFileText />
            </Box>
            <Box flex={1}>
              <Flex
                justifyContent={'space-between'}
                alignItems={'center'}
                gap={'2'}
              >
                <Text
                  fontSize={'14px'}
                  fontWeight={'600'}
                  title={invoiceItemsText}
                  lineClamp={'2'}
                >
                  {invoiceItemsText || 'No items'}
                </Text>
                <Flex alignItems={'center'}>
                  <Box fontSize={'sm'} textTransform={'capitalize'}>
                    {activity?.status ? (
                      <Status
                        isBorder={false}
                        isDot={false}
                        name={activity?.status}
                      />
                    ) : null}
                  </Box>
                  {session_type && (
                    <Center
                      px={1}
                      py={1}
                      fontWeight="medium"
                      maxH={'fit'}
                      ml={'2'}
                      rounded="md"
                      fontSize={'sm'}
                      color={getSessionColor(session_type).color}
                      border={getSessionColor(session_type).borderColor}
                      bg={getSessionColor(session_type).bgColor}
                      maxW={'fit-content'}
                    >
                      {session_type}
                    </Center>
                  )}
                </Flex>
              </Flex>
              <Flex alignItems={'center'} gap={'2rem'} py={'.3rem'} mt={'2'}>
                <Flex gap={'.3rem'} alignItems={'center'}>
                  <FiCalendar color={'#374151'} size={14} />
                  <Text color={'#374151'} fontWeight={'500'} fontSize={'sm'}>
                    {moment(activity?.invoice_date)
                      .utc()
                      .format('MMMM D, YYYY')}
                  </Text>
                </Flex>

                <Flex gap={'.3rem'} alignItems={'center'}>
                  {data?.organization_id === 1 && ( // Only show if Teams plan
                    <>
                      <FiUser color={'#374151'} />
                      <Text
                        color={'#374151'}
                        fontWeight={'500'}
                        fontSize={'sm'}
                      >
                        {slp} -
                      </Text>
                    </>
                  )}
                  <Flex gap={'.3rem'} alignItems={'center'}>
                    <Text fontSize={'sm'} color={'#374151'} fontWeight={'500'}>
                      {formatMoney(invoiceTotal)}
                    </Text>
                  </Flex>
                  {/* <Text fontSize={'sm'} color={'gray.300'}>
                    {activity?.status === 'PAID' ? '(Paid)' : '(Unpaid)'}
                  </Text> */}
                </Flex>
              </Flex>
            </Box>
          </Box>
        </Flex>
      </Flex>
    </Box>
  );
}
