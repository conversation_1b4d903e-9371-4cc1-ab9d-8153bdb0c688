//OPTIMIZED
import { queryK<PERSON> } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getTableData() {
  const response = await fetch(`/api/overview`, { method: 'GET' });
  const json = await response.json();
  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch stats');
  }
  return json;
}

type QueryFnType = typeof getTableData;

export const useGetOverviewTableApi = (
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.overview.getStats],
    queryFn: getTableData,
    ...config,
  });
};
