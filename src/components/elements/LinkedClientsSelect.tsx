import { useGetClientByIdQuery } from '@/api/clients/get-client-by-id';
import { useGetLinkedClientsQuery } from '@/api/linked_clients/get-linked-clients';
import { Box } from '@chakra-ui/react';
import React, { useState } from 'react';
import CustomSelect from '../Input/CustomSelect';
//import { getLinkedClientOptionsFromArray } from '@/utils/get-options-from-array';

export default function LinkedClientsSelect({ handleSelect, client_id }: any) {
  const [selectedClientId, setSelectedClientId] = useState(client_id);
  const { data: Client, isLoading: ClientsLoading } =
    useGetClientByIdQuery(client_id);
  const { data: LinkedClients, isLoading: LinkedClientsLoading } =
    useGetLinkedClientsQuery(client_id);

  // const linkedClients = LinkedClients?.reduce((acc: any, item: any) => {
  //   const clientData =
  //     item.client_id === client_id
  //       ? item.added_client
  //       : item.added_client_id === client_id
  //         ? item.client
  //         : null;

  //   if (clientData) {
  //     acc.push({
  //       label: `${clientData.first_name} ${clientData.last_name}`,
  //       value:
  //         item.client_id === client_id ? item.added_client_id : item.client_id,
  //     });
  //   }

  //   return acc;
  // }, []);

  // const linkedClientOptions: any = getLinkedClientOptionsFromArray(
  //   LinkedClients || []
  // );

  const linkedClientOptions = React.useMemo(() => {
    if (!LinkedClients || !Client) return [];

    const options = [];

    // Add the main client first
    options.push({
      label: `${Client.first_name} ${Client.last_name}`,
      value: Client.id,
    });

    // Add linked clients
    LinkedClients.forEach((item: any) => {
      if (item.client_id === client_id && item.added_client) {
        // Current client is the main client, add the added_client
        options.push({
          label: `${item.added_client.first_name} ${item.added_client.last_name}`,
          value: item.added_client.id,
        });
      } else if (item.added_client_id === client_id && item.client) {
        // Current client is the added_client, add the main client
        options.push({
          label: `${item.client.first_name} ${item.client.last_name}`,
          value: item.client.id,
        });
      }
    });

    return options;
  }, [LinkedClients, Client, client_id]);

  if (ClientsLoading || LinkedClientsLoading) {
    return null;
  }
  return (
    <div>
      {linkedClientOptions?.length > 1 && (
        <Box mb={'1rem'} w={'15rem'}>
          <CustomSelect
            placeholder="Select one"
            options={linkedClientOptions}
            onChange={(val) => {
              handleSelect(val.value);
              setSelectedClientId(val.value);
            }}
            label="Linked clients"
            defaultValue={linkedClientOptions?.find(
              (item: any) => Number(item?.value) === Number(selectedClientId)
            )}
            value={selectedClientId || Client?.id}
          />
        </Box>
      )}
    </div>
  );
}
