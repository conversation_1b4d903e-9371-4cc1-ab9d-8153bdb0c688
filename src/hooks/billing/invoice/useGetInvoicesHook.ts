import { fetchInvoicesWithLimit } from '@/api/invoices';
import { useGetAlInvoicesQuery } from '@/api/invoices/find-all';
import { findAllSlpNotes } from '@/api/slp_notes';
import { useGetAllSlpQuery } from '@/api/users/get-slps';
import { toaster } from '@/components/ui/toaster';
import { ToastMessages } from '@/constants/toast-messages';
import supabase from '@/lib/supabase/client';
import { IGetInvoicesFilterState } from '@/store/filters/invoices';
import { getDateString } from '@/utils/date-formatter';
import { formatMemo } from '@/utils/string-formatter';
import { useState } from 'react';
import { useRecoilState } from 'recoil';

export const useGetInvoicesHook = () => {
  const [filter, setFilter] = useRecoilState(IGetInvoicesFilterState);
  const {
    data,
    isLoading,
    refetch: invRefetch,
  } = useGetAlInvoicesQuery() as any;

  const sessionType = [
    {
      label: 'Ax',
      value: 'Ax',
    },
    {
      label: 'Tx',
      value: 'Tx',
    },
  ];

  const [linkLoading, setLinkLoading] = useState(false);
  const [search, setSearch] = useState('');
  // const [slpFilter,setSlp]

  const raw =
    typeof window !== 'undefined' ? localStorage.getItem('UserState') : null;
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  const { data: GSapi, isLoading: GSloading } = useGetAllSlpQuery({
    role: 'therapist',
    status: 'Active',
    organization_id: org?.id,
  });

  const handleSessionTypeChange = (session: string | null) => {
    if (session === null || session === undefined) {
      setFilter((prevFilter) => ({
        ...prevFilter,
        sessionType: undefined,
      }));
      return;
    }

    setFilter((prevFilter) => ({
      ...prevFilter,
      sessionType: session,
      page: 1, // Reset to first page when filtering
      currentPage: 1,
    }));
  };

  const handleSlpChange = (slpId: number | null) => {
    if (slpId === null || slpId === undefined) {
      setFilter((prevFilter) => ({
        ...prevFilter,
        selectedSlp: [],
      }));
      return;
    }

    let arr = filter.selectedSlp || [];
    if (arr?.includes(slpId)) {
      arr = arr.filter((item) => item !== slpId);
    } else {
      arr = [...arr, slpId];
    }

    setFilter((prevFilter) => ({
      ...prevFilter,
      selectedSlp: arr,
      page: 1, // Reset to first page when filtering
      currentPage: 1,
    }));
  };

  const linkSlpToInvoice = async () => {
    try {
      setLinkLoading(true);

      const allInvoices = await fetchInvoicesWithLimit(1000);
      const allSlpNote = await findAllSlpNotes(supabase);
      // console.log('all invoices is ', allInvoices);
      // console.log('all slpnote is ', allSlpNote);
      const arr: any = [];
      allInvoices.forEach((invoice: any) => {
        allSlpNote.forEach((slp_note: any) => {
          if (
            formatMemo(invoice?.memo) === formatMemo(slp_note?.invoice_memo) &&
            invoice?.slp_id === slp_note?.slp_id &&
            invoice.client_id === slp_note?.client_id &&
            getDateString(invoice.invoice_date) ===
              getDateString(slp_note.note_date)
          ) {
            arr.push({ slp_id: slp_note.id, invoice_id: invoice?.id });
            // console.log(
            //   'a match, INVOICE : ',
            //   invoice.id,
            //   ' SLP : ',
            //   slp_note.id
            // );
          }
        });
      });
      // return
      await supabase.rpc('link_slp_to_invoice', {
        slp_invoice_data: arr,
      });
      // console.log("response is ", response);
    } catch (error: any) {
      toaster.create({
        type: 'error',
        description: error?.message || ToastMessages.somethingWrong,
      });
    } finally {
      setLinkLoading(false);
    }
  };
  console.log('data is ', data);

  return {
    data,
    isLoading: isLoading || GSloading,
    slpOptions: GSapi?.map((item: any) => ({
      label: item.first_name,
      value: item?.id,
    })),
    filter,
    setFilter,
    invRefetch,
    handleSlpChange,
    handleSessionTypeChange,
    sessionType,
    linkSlpToInvoice,
    search,
    setSearch,
    linkLoading,
  };
};
